"""
This is the controller of code generation.
Do not import any files from app/ as it will break the code generation.
Please check twice before deploying after making changes to this file,
otherwise, the code generation will break.
"""

import asyncio
import threading
import time
from fastapi import FastAPI, HTTPException, Query, status, Request, Response, Body
from fastapi.responses import JSONResponse, HTMLResponse
from pydantic import BaseModel, HttpUrl
from typing import Dict, Optional, Literal, Any
from datetime import datetime, timezone
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.middleware.cors import CORSMiddleware
from pathlib import Path
import subprocess
import logging
import json
from fastapi import APIRouter, HTTPException, BackgroundTasks
import re
import html
import os
import uuid
import hashlib
import httpx
from app.connection.tenant_middleware import get_tenant_id
from app.utils.kg_build.sync_ebs_efs import do_sync, get_project_paths
from app.utils.logs_CGA_utils import update_config_dir
from app.core.Settings import settings as app_settings
from app.utils.docker_utils import ensure_docker_container_running, update_directory_permissions
from app.utils.k8.k8_utils import  check_pod_usage_status
from app.utils.k8.delete_project import delete_kubernetes_deployment
from app.utils.code_generation_utils import get_codegeneration_path
from app.connection.tenant_middleware import tenant_context


# Router definition
_SHOW_NAME = "code_gen"


app = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}}
)
try:
    from app.utils.k8.delete_project import delete_kubernetes_deployment
    KUBERNETES_MANAGER_AVAILABLE = True
except ImportError:
    logging.warning("Kubernetes resource manager not available, using fallback cleanup method")
    KUBERNETES_MANAGER_AVAILABLE = False

from datetime import datetime, timedelta

print("CODE GEN BASE PATH : ", Path(__file__).parent.parent.parent.absolute())

class Settings:
    MAX_WAIT_TIME: int = 60
    SESSION_PREFIX: str = "codegen"  # Changed from SESSION_NAME to SESSION_PREFIX
    WARMUP_SESSION_NAME: str = "warmup-session"  # Added for the warm-up script
    BASE: Path = Path(__file__).parent.parent.parent.absolute()
    TARGET_DIR: Path = Path("/home/<USER>/workspace")
    DOTENV: Path = BASE / ".env"
    LOGS_DIR: Path = BASE / "logs"
    APP_LOG_FILE: Path = LOGS_DIR / "app.log"
    CODEGEN_LOG_FILE: Path = LOGS_DIR / "codegen.log"
    WARMUP_LOG_FILE: Path = LOGS_DIR / "warmup.log"  # Added for warm-up logs
    STAGES: list[str] = ["dev", "qa", "prod", "experimental"]
    KAVIA_ROOT_TENANT_ID: str = "T0000"
    CODEGEN: bool = True
    LLM_MODEL: str = "claude-3-5-sonnet"
    MONGO_URI: str = os.getenv("MONGO_CONNECTION_URI")
    
    @classmethod
    def get_session_name(cls, container_id: int) -> str:
        """Generate session name using container ID"""
        return f"{cls.SESSION_PREFIX}-{container_id}"
    
    @classmethod
    def get_screen_commands(cls, container_id: int) -> Dict[str, list[str]]:
        session_name = cls.get_session_name(container_id)
        return {
            "start": ['screen', '-L', '-Logfile', str(cls.CODEGEN_LOG_FILE), '-dmS', 
                     session_name, 'python', 'app/batch_jobs/jobs.py'],
            "stop": ['screen', '-S', session_name, '-X', 'quit']
        }

settings = Settings()

class InputArguments(BaseModel):
    project_id: int
    architecture_id: Optional[int] = None  
    container_id: Optional[int] = None     
    task_id: str
    llm_model: str
    tenant_id: str = settings.KAVIA_ROOT_TENANT_ID
    agent_name: str = "CodeGeneration"     # Added this field
    platform: str = "common"
    retry: bool = False

    class Config:
        frozen = True

class SessionData(BaseModel):
    project_id: int
    architecture_id: Optional[int] = None
    task_id: str
    session_name: str
    start_time: datetime
    path: str

class CustomFormatter(logging.Formatter):
    def format(self, record):
        if isinstance(record.msg, dict):
            record.msg = json.dumps(record.msg)
        return super().format(record)

class LogManager:
    def __init__(self, app_log_file: Path, codegen_log_file: Path):
        self.app_log_file = app_log_file
        self.codegen_log_file = codegen_log_file
        
    def initialize_logs(self):
        """Initialize both app and codegen log files"""
        timestamp = datetime.now().isoformat()
        header = f"-------------- LOGS STARTED AT - {timestamp} --------------\n"
        
        for log_file in [self.app_log_file, self.codegen_log_file]:
            log_file.parent.mkdir(parents=True, exist_ok=True)
            if not log_file.exists():
                log_file.write_text(header)

    def setup_logging(self):
        """Configure logging settings"""
        # Ensure the log directory exists
        self.app_log_file.parent.mkdir(parents=True, exist_ok=True)

        # Get the root logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)

        # Remove all existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # File Handler
        file_handler = logging.FileHandler(str(self.app_log_file))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console Handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        logger.info("Logging initialized")

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Only log paths that are part of our application
        app_paths = ["/start", "/stop", "/status", "/clone_repo", "/view_logs"]
        if request.url.path not in app_paths:
            return await call_next(request)

        request_id = str(uuid.uuid4())
        start_time = datetime.now(timezone.utc)

        # Log the request
        request_log = {
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params)
        }

        logging.info(f"Request: {json.dumps(request_log, indent=2)}")

        # Process the request and capture the response
        response = await call_next(request)
        
        # Log the response
        process_time = (datetime.now(timezone.utc) - start_time).total_seconds()
        
        response_log = {
            "request_id": request_id,
            "status_code": response.status_code,
            "processing_time": f"{process_time:.3f}s"
        }

        # Try to capture response body for JSON responses
        if response.headers.get("content-type") == "application/json":
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            
            try:
                response_log["body"] = json.loads(response_body.decode())
            except:
                response_log["body"] = None

            # Create a new response with the same body
            return Response(
                content=response_body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )

        logging.info(f"Response: {json.dumps(response_log, indent=2)}")
        return response

class ScreenSessionManager:
    @staticmethod
    def is_running(session_name: str) -> bool:
        try:
            # Run screen -ls and capture output
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session detection using proper parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line and not line.endswith('(Dead)'):
                    logging.info(f"Screen session '{session_name}' is currently running.")
                    return True
            logging.info(f"No running session found for name: '{session_name}'")
            return False
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return False
            logging.error(f"Error checking screen sessions: {e.output}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error checking screen sessions: {str(e)}")
            return False

    @staticmethod
    def get_session_details(session_name: str) -> Dict[str, str]:
        """Get detailed information about a screen session"""
        try:
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            # More robust session parsing
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            for line in session_lines:
                if f".{session_name}\t" in line:
                    parts = line.strip().split('\t')
                    session_id = parts[0].strip()
                    status = 'Detached'
                    if len(parts) > 2:
                        status_part = parts[-1].strip('()')
                        status = status_part if status_part != 'Detached' else 'Detached'
                    
                    return {
                        "session_id": session_id,
                        "name": session_name,
                        "status": status
                    }
            return {}
            
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return {}
            logging.error(f"Error getting session details: {e.output}")
            return {}
        except Exception as e:
            logging.error(f"Unexpected errors getting session details: {str(e)}")
            return {}

    @staticmethod
    def stop_session(session_name: str) -> bool:
        """Stop a screen session safely"""
        try:
            if not ScreenSessionManager.is_running(session_name):
                return True
                
            # Try graceful quit first
            subprocess.run(
                ['screen', '-S', session_name, '-X', 'quit'],
                check=True,
                capture_output=True,
                text=True
            )
            
            # Verify session was stopped
            if ScreenSessionManager.is_running(session_name):
                # If still running, try force kill
                logging.warning(f"Session {session_name} still running after quit, attempting force kill")
                session_details = ScreenSessionManager.get_session_details(session_name)
                if session_details and "session_id" in session_details:
                    subprocess.run(
                        ['screen', '-S', session_details["session_id"], '-X', 'kill'],
                        check=True,
                        capture_output=True,
                        text=True
                    )
            
            return not ScreenSessionManager.is_running(session_name)
            
        except Exception as e:
            logging.error(f"Error stopping session {session_name}: {str(e)}")
            return False
    
    @staticmethod
    def get_all_sessions() -> list[Dict[str, Any]]:
        """Get all running screen sessions with their details and creation time"""
        try:
            output = subprocess.check_output(
                ['screen', '-ls'], 
                stderr=subprocess.STDOUT,
                text=True
            )
            
            sessions = []
            session_lines = [line.strip() for line in output.splitlines() if '\t' in line]
            
            for line in session_lines:
                if '(Dead)' in line:
                    continue
                    
                parts = line.strip().split('\t')
                full_id = parts[0].strip()
                
                # Extract the PID and creation time
                pid_with_timestamp = full_id.split('.')[0]
                
                # Try to get creation time from process
                creation_time = None
                try:
                    pid = int(pid_with_timestamp)
                    # Get process creation time using ps command
                    ps_output = subprocess.check_output(
                        ['ps', '-p', str(pid), '-o', 'lstart='],
                        text=True
                    ).strip()
                    
                    if ps_output:
                        # Parse the date format: 'Wed May 6 12:45:49 2023'
                        creation_time = datetime.strptime(ps_output, '%a %b %d %H:%M:%S %Y')
                except (ValueError, subprocess.CalledProcessError):
                    # If we can't get the exact time, use current time minus 1 second
                    # This is just a fallback and won't be accurate
                    creation_time = datetime.now() - timedelta(seconds=1)
                
                # Extract session name from the full ID
                session_name = full_id.split('.', 1)[1] if '.' in full_id else ""
                
                status = 'Detached'
                if len(parts) > 2:
                    status_part = parts[-1].strip('()')
                    status = status_part if status_part != 'Detached' else 'Detached'
                
                sessions.append({
                    "session_id": full_id,
                    "pid": pid_with_timestamp,
                    "name": session_name,
                    "status": status,
                    "creation_time": creation_time
                })
            
            return sessions
            
        except subprocess.CalledProcessError as e:
            if "No Sockets found" in str(e.output):
                logging.info("No screen sessions found")
                return []
            logging.error(f"Error listing screen sessions: {e.output}")
            return []
        except Exception as e:
            logging.error(f"Unexpected error listing screen sessions: {str(e)}")
            return []
    
    @staticmethod
    def check_and_terminate_old_sessions(max_age_hours: int = 2, 
                                        inactive_days: int = 2,
                                        auto_terminate_func=None) -> None:
        """
        Check for and terminate old screen sessions.
        
        Args:
            max_age_hours: Maximum age in hours for a running session before termination
            inactive_days: Number of days of inactivity before terminating the pod
            auto_terminate_func: Optional callback function to trigger pod termination
        """
        try:
            logging.info(f"Checking for old screen sessions (max age: {max_age_hours}h, inactive: {inactive_days}d)")
            
            # Get all running sessions
            sessions = ScreenSessionManager.get_all_sessions()
            
            if not sessions:
                logging.info("No active screen sessions found")
                
                # Check if no sessions have been created for inactive_days
                last_activity_file = Path("/tmp/last_screen_activity.txt")
                current_time = datetime.now()
                
                if not last_activity_file.exists():
                    # First run, create the file
                    last_activity_file.write_text(current_time.isoformat())
                    logging.info(f"Created last activity tracking file at {last_activity_file}")
                    return
                
                # Read last activity time
                try:
                    last_activity_str = last_activity_file.read_text().strip()
                    last_activity = datetime.fromisoformat(last_activity_str)
                    
                    # Calculate days since last activity
                    days_inactive = (current_time - last_activity).days
                    logging.info(f"Days since last screen activity: {days_inactive}")
                    
                    # If inactive for too long, trigger auto-termination
                    if days_inactive >= inactive_days and auto_terminate_func:
                        logging.warning(f"No screen activity for {days_inactive} days, triggering pod termination")
                        auto_terminate_func()
                except (ValueError, IOError) as e:
                    # If file exists but can't be read properly, reset it
                    logging.error(f"Error reading last activity file: {e}")
                    last_activity_file.write_text(current_time.isoformat())
                
                return
            
            # Update the last activity file since we have sessions
            last_activity_file = Path("/tmp/last_screen_activity.txt")
            last_activity_file.write_text(datetime.now().isoformat())
            
            # Current time for age calculation
            current_time = datetime.now()
            
            for session in sessions:
                if session["creation_time"] is None:
                    logging.warning(f"Session {session['name']} has no creation time, skipping")
                    continue
                
                # Calculate age in hours
                age_hours = (current_time - session["creation_time"]).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    logging.warning(f"Session {session['name']} is {age_hours:.2f} hours old, exceeding max age of {max_age_hours} hours")
                    
                    # Stop the session
                    if ScreenSessionManager.stop_session(session["name"]):
                        logging.info(f"Successfully terminated old session: {session['name']}")
                    else:
                        logging.error(f"Failed to terminate old session: {session['name']}")
            
            # Check if auto-termination should be triggered
            if not sessions and auto_terminate_func:
                logging.info("No active sessions after cleanup, checking termination criteria")
                ScreenSessionManager.check_termination_criteria(auto_terminate_func)
                
        except Exception as e:
            logging.error(f"Error during session cleanup: {str(e)}")
    
    @staticmethod
    def check_termination_criteria(auto_terminate_func) -> None:
        """Check if the pod should be terminated based on criteria"""
        try:
            # Get project_id and env_name from environment variables
            project_id = os.environ.get("PROJECT_ID")
            env_name = os.environ.get("ENV_NAME", "dev")
            
            if project_id:
                logging.info(f"Termination criteria met for project {project_id}, environment {env_name}")
                auto_terminate_func(project_id=project_id, env_name=env_name)
            else:
                logging.warning("Cannot auto-terminate: No PROJECT_ID environment variable found")
                # Fall back to generic termination function if available
                auto_terminate_func()
        except Exception as e:
            logging.error(f"Error in termination criteria check: {str(e)}")

class EnvManager:
    def __init__(self, base_path: Path):
        self.base_path = base_path

    def update_env_file(self, stage: str, input_args: InputArguments, custom_env_vars: Dict[str, str] = {}) -> None:
        env_stage_file = self.base_path / f'.env.{stage}'
        env_file = self.base_path / '.env'

        if not env_stage_file.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Environment file for stage {stage} not found"
            )

        env_vars = self._read_env_file(env_stage_file)
        env_vars.update({
            'FEATURE_FLAG_GIT_TOOL':True,
            'FEATURE_FLAG_USE_DOCKER':True,
            'BATCH_JOB_TRIGGER':True,
            'BATCH_JOB_STAGE': stage,
            'input_arguments': json.dumps(input_args.dict())
        })
        if custom_env_vars != {}:
            env_vars.update(custom_env_vars)
        print(f"Updated env vars: {env_vars}")
        logging.info(f"Updated env vars: {env_vars}")
        self._write_env_file(env_file, env_vars)

    @staticmethod
    def _read_env_file(path: Path) -> Dict[str, str]:
        if not path.exists():
            return {}
        
        env_vars = {}
        for line in path.read_text().splitlines():
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                env_vars[key] = value
        return env_vars

    @staticmethod
    def _write_env_file(path: Path, env_vars: Dict[str, str]) -> None:
        content = '\n'.join(f"{k}={v}" for k, v in env_vars.items())
        path.write_text(content + '\n')

# Response Models
class StartResponse(BaseModel):
    message: str
    session_data: Optional[SessionData]

class StatusResponse(BaseModel):
    status: str
    session_data: Optional[SessionData]

class StopResponse(BaseModel):
    message: str
    session_data: Optional[SessionData]

class CloneResponse(BaseModel):
    message: str
    output: Optional[str]
    path: str

class LogResponse(BaseModel):
    log_type: str
    log_content: str


# Function to start the warm-up script in a separate screen session
def start_warmup_script() -> None:
    """Start the warm-up script in a separate screen session"""
    session_name = settings.WARMUP_SESSION_NAME
    
    # Check if the warmup script is already running
    if ScreenSessionManager.is_running(session_name):
        logging.info("Warm-up script is already running")
        return
    
    try:
        # Create log file if it doesn't exist
        warmup_log_file = settings.WARMUP_LOG_FILE
        if not warmup_log_file.parent.exists():
            warmup_log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Start the warm-up script in a screen session
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)
        env["STAGE"] ="dev"
        subprocess.run(
            ['screen', '-L', '-Logfile', str(warmup_log_file), 
             '-dmS', session_name, 'python', 'warm_up.py'],
            check=True,
            env=env
        )
        
        logging.info("Warm-up script started successfully in background")
    except Exception as e:
        logging.error(f"Error starting warm-up script: {e}")
        # We don't want to stop the application if the warm-up script fails

# Function to stop the warm-up script
def stop_warmup_script() -> None:
    """Stop the warm-up script screen session"""
    session_name = settings.WARMUP_SESSION_NAME
    
    if not ScreenSessionManager.is_running(session_name):
        logging.info("No warm-up script is currently running")
        return
    
    try:
        if ScreenSessionManager.stop_session(session_name):
            logging.info("Warm-up script has been stopped")
        else:
            logging.error("Failed to stop the warm-up script")
    except Exception as e:
        logging.error(f"Error stopping warm-up script: {e}")

## Initialize settings first
settings = Settings()

# Initialize managers
log_manager = LogManager(settings.APP_LOG_FILE, settings.CODEGEN_LOG_FILE)

# Create log directories and initialize logging
settings.LOGS_DIR.mkdir(parents=True, exist_ok=True)
log_manager.initialize_logs()
log_manager.setup_logging()

# Initialize other components
env_manager = EnvManager(settings.BASE)
session_data: Optional[SessionData] = None

# Ensure PYTHONPATH is set
os.environ["PYTHONPATH"] = str(settings.BASE)
logging.info(f"PYTHONPATH set to: {os.environ['PYTHONPATH']}")

def setup_alternative_logging(tenant_id: str, project_id: str) -> logging.Logger:
    """
    Set up alternative logging to /efs/termination_logs/{tenant_id}/{project_id}
    
    Args:
        tenant_id: The tenant identifier
        project_id: The project identifier
        
    Returns:
        logging.Logger: Configured logger instance for alternative logging
    """
    try:
        # Create the directory structure
        log_dir = Path(f"/efs/termination_logs/{tenant_id}/{project_id}")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create log filename with timestamp
        log_filename = f"session_monitor_{datetime.now().strftime('%Y%m%d')}.log"
        log_file_path = log_dir / log_filename
        
        # Create a separate logger for alternative logging
        alt_logger = logging.getLogger(f"session_monitor_alt_{tenant_id}_{project_id}")
        alt_logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in alt_logger.handlers[:]:
            alt_logger.removeHandler(handler)
        
        # Create file handler with rotation-friendly naming
        file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # Create formatter for alternative logs
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [PID:%(process)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        alt_logger.addHandler(file_handler)
        
        # Prevent propagation to avoid duplicate logs in main logger
        alt_logger.propagate = False
        
        # Log initial setup message
        alt_logger.info(f"Alternative logging initialized for tenant_id={tenant_id}, project_id={project_id}")
        alt_logger.info(f"Log file: {log_file_path}")
        
        return alt_logger
        
    except Exception as e:
        logging.error(f"Failed to setup alternative logging: {str(e)}")
        # Return a dummy logger that logs to main logger as fallback
        fallback_logger = logging.getLogger("session_monitor_fallback")
        fallback_logger.addHandler(logging.StreamHandler())
        return fallback_logger

def log_to_both(main_logger: logging.Logger, alt_logger: logging.Logger, level: str, message: str):
    """
    Log message to both main logger and alternative logger
    
    Args:
        main_logger: Main application logger
        alt_logger: Alternative file logger
        level: Log level ('info', 'warning', 'error', 'debug')
        message: Message to log
    """
    log_methods = {
        'info': (main_logger.info, alt_logger.info),
        'warning': (main_logger.warning, alt_logger.warning),
        'error': (main_logger.error, alt_logger.error),
        'debug': (main_logger.debug, alt_logger.debug)
    }
    
    if level.lower() in log_methods:
        main_method, alt_method = log_methods[level.lower()]
        main_method(message)
        alt_method(message)
    else:
        main_logger.info(message)
        alt_logger.info(message)

def start_session_monitor(check_interval_minutes: int = 30):
    """
    Enhanced session monitor that checks both local flags and ConfigMap for pod usage.
    Now includes alternative logging to /efs/termination_logs/{tenant_id}/{project_id}
    """
    tenant_id = get_tenant_id()
    project_id = os.environ.get("PROJECT_ID", "")
    env_name = os.environ.get("ENVIRONMENT", "dev")
    
    # Setup alternative logging
    alt_logger = None
    if tenant_id and project_id:
        alt_logger = setup_alternative_logging(tenant_id, project_id)
    else:
        logging.warning("Missing tenant_id or project_id, alternative logging disabled")
    
    # Get main logger
    main_logger = logging.getLogger(__name__)
    
    def log_both(level: str, message: str):
        """Helper function to log to both loggers if alternative logger exists"""
        if alt_logger:
            log_to_both(main_logger, alt_logger, level, message)
        else:
            getattr(main_logger, level.lower(), main_logger.info)(message)
    
    if project_id:
        log_both('info', f"Session monitor using project ID: {project_id}")
    else:
        log_both('warning', "No PROJECT_ID found in environment, auto-termination may be limited")
    
    def monitor_thread_func():
        INACTIVE_HOURS_THRESHOLD = 0.03
        MAX_SESSION_AGE_HOURS = 16
        DOCKER_CHECK_ENABLED = False
        
        log_both('info', f"Enhanced session monitor started. Checking every {check_interval_minutes} minutes.")
        log_both('info', f"Alternative logging enabled: {alt_logger is not None}")
        
        while True:
            try:
                log_both('info', "=== Starting monitoring cycle ===")
                log_both('info', "Updating directory permissions...")
                update_directory_permissions()
                
                # 1. Docker Container Monitoring
                if DOCKER_CHECK_ENABLED:
                    log_both('info', "Checking Docker container status...")
                    container_running = ensure_docker_container_running()
                    if not container_running:
                        log_both('error', "Failed to ensure Docker container is running")
                    else:
                        log_both('info', "Docker container is running properly")
                
                # 2. Screen Session Monitoring
                log_both('info', "Checking screen sessions...")
                sessions = ScreenSessionManager.get_all_sessions()
                                       
                if sessions:
                    log_both('info', f"Found {len(sessions)} active screen sessions")
                    
                    # Update activity tracking
                    last_activity_file = Path("/tmp/last_screen_activity.txt")
                    current_time_iso = datetime.now().isoformat()
                    last_activity_file.write_text(current_time_iso)
                    
                    # Mark pod as used locally if not already marked
                    pod_used_flag_file = Path("/tmp/pod_used.flag")
                    if not pod_used_flag_file.exists():
                        pod_used_flag_file.write_text(current_time_iso)
                        log_both('info', "Pod marked as used - recorded screen activity")
                    
                    # Check session ages and terminate old ones
                    current_time = datetime.now()
                    terminated_any = False
                    
                    for session in sessions:
                        if session.get("creation_time") is None:
                            continue
                            
                        age_hours = (current_time - session["creation_time"]).total_seconds() / 3600
                        log_both('info', f"Session {session['name']} is {age_hours:.2f} hours old")
                        
                        if age_hours > MAX_SESSION_AGE_HOURS:
                            log_both('warning', f"Session {session['name']} exceeds max age, terminating")
                            
                            if ScreenSessionManager.stop_session(session["name"]):
                                log_both('info', f"Successfully terminated old session: {session['name']}")
                                terminated_any = True
                            else:
                                log_both('error', f"Failed to terminate old session: {session['name']}")
                    
                    if terminated_any:
                        remaining_sessions = ScreenSessionManager.get_all_sessions()
                        if not remaining_sessions:
                            log_both('info', "No active sessions remaining after cleanup")
                else:
                    log_both('info', "No active screen sessions found")
                    
                    # Check if pod has been used (either locally or via ConfigMap)
                    if project_id:
                        pod_has_been_used = check_pod_usage_status(project_id, env_name)
                        
                        if pod_has_been_used:
                            # Pod has been used, check for idleness
                            last_activity_file = Path("/tmp/last_screen_activity.txt")
                            
                            if last_activity_file.exists():
                                try:
                                    last_activity_str = last_activity_file.read_text().strip()
                                    last_activity = datetime.fromisoformat(last_activity_str)
                                    
                                    hours_inactive = (datetime.now() - last_activity).total_seconds() / 3600
                                    log_both('info', f"Hours since last screen activity: {hours_inactive:.2f}")
                                    
                                    if hours_inactive >= INACTIVE_HOURS_THRESHOLD:
                                        log_both('warning', f"Pod idle for {hours_inactive:.2f} hours, triggering termination")
                                        
                                        try:
                                            log_both('info', f"Successfully deleted pod from MongoDB: {project_id}")
                                            try:
                                                push_to_current_branch()
                                            except Exception as e:
                                                log_both('error', f"Error pushing to current branch: {str(e)}")
                                                pass
                                            delete_kubernetes_deployment(project_id=project_id, env_name=env_name)
                                            
                                            log_both('info', f"Kubernetes deployment deletion initiated for project: {project_id}")
                                                
                                        except Exception as e:
                                            log_both('error', f"Error deleting pod : {str(e)}")
                                except (ValueError, IOError) as e:
                                    log_both('error', f"Error reading last activity file: {e}")
                                    last_activity_file.write_text(datetime.now().isoformat())
                            else:
                                last_activity_file.write_text(datetime.now().isoformat())
                                log_both('info', "Created last activity tracking file")
                        else:
                            log_both('info', "Pod is in ready state (never used). Skipping idleness check.")
                    else:
                        log_both('warning', "No PROJECT_ID available for pod usage check")
                
                log_both('info', "=== Monitoring cycle completed ===")
                
            except Exception as e:
                log_both('error', f"Error in session monitor: {str(e)}")
                # Log additional context for debugging in alternative logger
                if alt_logger:
                    alt_logger.error(f"Stack trace: {str(e)}", exc_info=True)
            
            time.sleep(check_interval_minutes * 60)

    # Check if monitor thread is already running
    for thread in threading.enumerate():
        if thread.name == "session_monitor_thread":
            log_both('info', "Session monitor is already running")
            return
    
    # Start the monitor thread
    monitor_thread = threading.Thread(
        target=monitor_thread_func,
        name="session_monitor_thread",
        daemon=True
    )
    
    monitor_thread.start()
    log_both('info', "Enhanced session monitor thread started successfully")
    
    if alt_logger:
        alt_logger.info(f"Session monitor initialized with alternative logging for tenant_id={tenant_id}, project_id={project_id}")

def restart_codeserver() -> None:
    try:
        subprocess.run(
            ["docker", "restart", "my-codeserver"],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logging.info("Successfully restarted codeserver container")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error restarting codeserver container: {e.stderr.decode()}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error restarting codeserver: {e}")
        raise

@app.get("/outbound-check")
async def outbound_check():
    test_url = "https://example.com"  # Choose a reliable, known URL
    timeout = 5.0  # seconds

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(test_url)
            if response.status_code == 200:
                return {"status": "ok", "message": "Outbound connectivity is working."}
            else:
                raise HTTPException(status_code=503, detail="Unable to reach the test endpoint.")
    except httpx.RequestError as e:
        # This could happen due to DNS issues, network errors, etc.
        raise HTTPException(status_code=503, detail=f"Outbound check failed: {e}")

@app.get("/start", response_model=StartResponse)
async def start_command(
    project_id: int = Query(..., gt=0),
    architecture_id: int = Query(None, gt=0),  # Make optional for maintenance
    task_id: str = Query(..., min_length=1),
    stage: str = Query('dev', description="Stage to use for code generation"),
    llm_model: str = Query(settings.LLM_MODEL, description="LLM model to use for code generation"),
    tenant_id: str = Query(settings.KAVIA_ROOT_TENANT_ID, description="Tenant ID for multi-tenant support"),
    agent_name: str = Query("CodeGeneration", description="Agent name to use (CodeGeneration or CodeMaintenance)"),
    platform: str = Query("common", description="Platform to use for code generation"),
    user_id: str = Query(None, description="User ID for tracking purposes"),
):
    global session_data
    tenant_context.set(tenant_id)
    
    # Mark the pod as used when start_command is called
    try:
        pod_used_flag_file = Path("/tmp/pod_used.flag")
        if not pod_used_flag_file.exists():
            pod_used_flag_file.write_text(datetime.now().isoformat())
            logging.info("Pod marked as used via start_command")
        
        # Update last activity as well
        last_activity_file = Path("/tmp/last_screen_activity.txt")
        last_activity_file.write_text(datetime.now().isoformat())
    except Exception as e:
        print(f"Error marking pod as used: {e}")
        pass
    
    # For maintenance, use a fixed container_id since it's project-wide
    if agent_name == "DocumentCreation":
        session_name = f"document-creation-{project_id}"
        os.makedirs(f"/home/<USER>/workspace/{task_id}", exist_ok=True)
    elif agent_name == "CodeMaintenance":
        session_name = f"maintenance-{task_id}-{project_id}"
        os.makedirs(f"/home/<USER>/workspace/{task_id}", exist_ok=True)

    else:
        try:
            os.makedirs("/home/<USER>/workspace/code-generation", exist_ok=True)
        except:
            pass
        
        session_name = f"code-generation-{task_id}-{project_id}"

    if ScreenSessionManager.is_running(session_name):
        return StartResponse(
            message=f"{agent_name} Already Started!",
            session_data=session_data
        )

    try:
        # Create input args based on agent type
        input_args_dict = {
            "project_id": project_id,
            "task_id": task_id,
            "llm_model": llm_model,
            "tenant_id": tenant_id,
            "agent_name": agent_name,
            "platform": platform
        }
        
        if user_id:
            input_args_dict["user_id"] = user_id

        # Add architecture_id and container_id only for code generation
        if agent_name == "CodeGeneration":
            input_args_dict.update({
                "architecture_id": architecture_id
            })

        args_json = json.dumps(input_args_dict)
        
        # Update the screen command to include PYTHONPATH
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)
        env["input_arguments"] = args_json

        env["PROJECT_ID"] = str(project_id)
        env["ENV_NAME"] = stage
        env["DUPLO_ENV"] = 'True'
        env["BATCH_JOB_STAGE"] = stage
        env["BATCH_JOB_TRIGGER"] = 'True'
        ############
        input_args = json.loads(input_args_dict) if isinstance(input_args_dict, str) else input_args_dict
        project_id_ = int(input_args.get("project_id"))
        task_id_ = input_args.get("task_id")
        update_config_dir("config.ini",tenant_id=get_tenant_id(),project_id=project_id_,task_id=task_id_)
        # Create screen command based on agent type
        screen_cmd = [
            'screen', '-L', 
            '-Logfile', f"/efs/{get_tenant_id()}/{project_id}/logs/{task_id}/{task_id}_screen.log",
            '-dmS', session_name, 
            'bash', '-c',
           f"python app/batch_jobs/jobs.py --input_args '{args_json}' --stage {stage} | ts '[%Y-%m-%d %H:%M:%S]'"
        ]

        subprocess.run(
            screen_cmd,
            check=True,
            env=env
        )
        
        session_data = SessionData(
            project_id=project_id,
            architecture_id=architecture_id if agent_name == "CodeGeneration" else None,
            task_id=task_id,
            session_name=session_name,
            start_time=datetime.now(),
            path=str(settings.TARGET_DIR)
        )
        return StartResponse(
            message=f"{agent_name} executed successfully in the background",
            session_data=session_data
        )
    
    except Exception as e:
        logging.error(f"Error starting {agent_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/retry", response_model=StartResponse)
async def retry(
    project_id: int = Query(..., gt=0),
    architecture_id: int = Query(..., gt=0),
    container_id: int = Query(..., gt=0),
    task_id: str = Query(..., min_length=1),
    tenant_id: str = Query('', description="tenant_id"),
    stage: str = Query('dev', description="Stage to use for code generation"),
    llm_model: str = Query('claude-3-5-sonnet', description="LLM model to use for code generation")
):
    global session_data
    
    session_name = settings.get_session_name(container_id)
    
    if ScreenSessionManager.is_running(session_name):
        return StartResponse(
            message="Code Generation Already Started!",
            session_data=session_data
        )

    try:
        input_args = InputArguments(
            project_id=project_id,
            architecture_id=architecture_id,
            task_id=task_id,
            container_id=container_id,
            llm_model=llm_model,
            tenant_id=tenant_id,
            retry=True
        )
        
        env_manager.update_env_file(stage, input_args)
        
        # Update the screen command to include PYTHONPATH
        env = os.environ.copy()
        env["PYTHONPATH"] = str(settings.BASE)

        
        subprocess.run(
            settings.get_screen_commands(container_id)["start"],
            check=True,
            env=env
        )
        
        session_data = SessionData(
            project_id=project_id,
            architecture_id=architecture_id,
            task_id=task_id,
            session_name=session_name,
            start_time=datetime.now(),
            path=str(settings.TARGET_DIR)
        )
        
        return StartResponse(
            message="Command executed successfully in the background",
            session_data=session_data
        )
    
    except Exception as e:
        logging.error(f"Error starting command: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/status", response_model=StatusResponse)
async def get_status(container_id: int = Query(..., gt=0)):
    global session_data
    
    session_name = settings.get_session_name(container_id)
    # Get detailed session information
    session_details = ScreenSessionManager.get_session_details(session_name)
    is_running = bool(session_details)
    
    # Log detailed status for debugging
    logging.info(f"Session status check: {json.dumps(session_details, indent=2)}")
    
    if is_running:
        if session_data is None:
            # Session is running but we don't have session data
            logging.warning("Screen session is running but no session data found")
            return StatusResponse(
                status="running",
                session_data=None
            )
        return StatusResponse(
            status="running",
            session_data=session_data
        )
    else:
        # Clear session data and env file if not running
        if session_data is not None:
            logging.info("Clearing session data as process is not running")
            session_data = None
            settings.DOTENV.write_text('')
        
        return StatusResponse(
            status="not running",
            session_data=None
        )

@app.delete("/delete")
async def delete_command(
    background_tasks: BackgroundTasks
):
    background_tasks.add_task(
        delete_kubernetes_deployment
    )
    response = {"message":"Delete initiated....."}
    return response
    

@app.get("/stop", response_model=StopResponse)
async def stop_command(
    container_id: Optional[int] = Query(None, gt=0, description="Container ID for code generation"),
    project_id: Optional[int] = Query(None, gt=0, description="Project ID for code maintenance"),
    agent_name: str = Query("CodeGeneration", description="Agent name (CodeGeneration or CodeMaintenance)"),
    task_id: Optional[str] = Query(None, min_length=1, description="Task ID for code maintenance")
):
    global session_data
    
    # Validate inputs based on agent type
    if agent_name == "CodeMaintenance":
        if not project_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_id is required for code maintenance"
            )
        if not task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="task_id is required for code maintenance"
            )
        session_name = f"maintenance-{task_id}-{project_id}"
        
    elif agent_name == "DocumentCreation":
        if not project_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_id is required for document creation"
            )
        session_name = f"document-creation-{project_id}"

    else:
        if not container_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="container_id is required for code generation"
            )
        session_name = f"code-generation-{task_id}-{project_id}"
    
    if not ScreenSessionManager.is_running(session_name):
        return StopResponse(
            message=f"No {agent_name} process is currently running.",
            session_data=None
        )
    
    try:
        if ScreenSessionManager.stop_session(session_name):
            # Clear session data and env file after successful stop
            session_data = None
            settings.DOTENV.write_text('')
            
            return StopResponse(
                message=f"{agent_name} process has been stopped.",
                session_data=None
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to stop the {agent_name} process"
            )
            
    except Exception as e:
        logging.error(f"Error stopping {agent_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@app.get("/clone_repo", response_model=CloneResponse)
async def clone_repo(
    repo_url: HttpUrl = Query(..., description="The full URL of the AWS CodeCommit repository"),
    target_dir: str = Query(str(settings.TARGET_DIR), description="Optional target directory for cloning the repository")
):
    target_path = Path(target_dir)
    
    if not target_path.is_dir():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Target directory does not exist or is not a directory"
        )
    
    # Validate AWS CodeCommit URL
    if not re.match(
        r'^https://git-codecommit\.[a-z0-9-]+\.amazonaws\.com/v1/repos/[a-zA-Z0-9._-]+$',
        str(repo_url)
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid AWS CodeCommit repository URL format"
        )
    
    repo_name = str(repo_url).split('/')[-1]
    repo_path = target_path / repo_name
    
    if repo_path.is_dir():
        return CloneResponse(
            message="Repo has been already cloned!",
            path=str(repo_path)
        )
    
    try:
        process = subprocess.run(
            ["git", "clone", str(repo_url), repo_name],
            cwd=str(target_path),
            capture_output=True,
            text=True,
            check=True
        )
        
        logging.info(f"Repository cloned successfully into {target_path}: {process.stdout}")
        
        return CloneResponse(
            message=f"AWS CodeCommit repository '{repo_name}' cloned successfully",
            output=process.stdout,
            path=str(repo_path)
        )
    
    except subprocess.CalledProcessError as e:
        logging.error(f"Error cloning repository: {e.stderr}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cloning repository: {e.stderr}"
        )

@app.get("/view_logs", response_class=HTMLResponse)
async def view_log(
    log_type: Literal["app", "codegen"] = Query(..., description="Type of log to view: 'app' or 'codegen'"),
    password: str = Query(..., description="Password required to view logs")
):
    # kaviacodegen232
    # Check password hash
    expected_hash = "ed29e38addfafb71b9c8139bad4a9e94e513b9ba4f8001da75a6fbe902b04568"  # SHA256 hash of kavialogs232@
    provided_hash = hashlib.sha256(password.encode()).hexdigest()
    
    if provided_hash != expected_hash:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password"
        )
        
    log_file = settings.APP_LOG_FILE if log_type == "app" else settings.CODEGEN_LOG_FILE

    if not log_file.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Log file '{log_type}' not found"
        )
    
    try:
        log_content = log_file.read_text()
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{log_type.title()} Logs</title>
            <style>
                body {{
                    font-family: 'Courier New', monospace;
                    background: #1e1e1e;
                    color: #d4d4d4;
                    padding: 20px;
                    margin: 0;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .header {{
                    background: #2d2d2d;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    position: sticky;
                    top: 0;
                    z-index: 100;
                    background: #2d2d2d;
                }}
                .title {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #61dafb;
                }}
                .controls {{
                    display: flex;
                    gap: 10px;
                }}
                .log-viewer {{
                    background: #252526;
                    border-radius: 5px;
                    padding: 15px;
                    max-height: calc(100vh - 100px);
                    overflow-y: auto;
                }}
                .log-entry {{
                    border-bottom: 1px solid #333;
                    padding: 10px;
                    cursor: pointer;
                }}
                .log-entry:hover {{
                    background: #2d2d2d;
                }}
                .entry-header {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }}
                .timestamp {{
                    color: #569cd6;
                    white-space: nowrap;
                }}
                .level {{
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-weight: bold;
                    min-width: 60px;
                    text-align: center;
                }}
                .level-INFO {{
                    background: #294a3d;
                    color: #3c9;
                }}
                .level-WARNING {{
                    background: #4a4629;
                    color: #fc3;
                }}
                .level-ERROR {{
                    background: #4a2929;
                    color: #f66;
                }}
                .preview {{
                    color: #d4d4d4;
                    margin: 5px 0;
                    padding-left: 20px;
                }}
                .details {{
                    display: none;
                    background: #1e1e1e;
                    padding: 15px;
                    margin: 10px 0;
                    border-radius: 3px;
                    white-space: pre-wrap;
                    margin-left: 20px;
                }}
                .search {{
                    background: #3c3c3c;
                    border: 1px solid #555;
                    color: #fff;
                    padding: 5px 10px;
                    border-radius: 3px;
                    min-width: 200px;
                }}
                button {{
                    background: #0078d4;
                    color: white;
                    border: none;
                    padding: 5px 15px;
                    border-radius: 3px;
                    cursor: pointer;
                }}
                button:hover {{
                    background: #106ebe;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">{log_type.title()} Logs</div>
                    <div class="controls">
                        <input type="text" id="searchInput" class="search" 
                               placeholder="Search logs...">
                        <button onclick="window.location.reload()">Refresh</button>
                    </div>
                </div>
                <div class="log-viewer">
        """
        
        # Process log lines
        for line in log_content.splitlines():
            if not line.strip():
                continue
                
            # Parse log line with regex
            match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (\w+) - (.+)', line)
            if match:
                timestamp, level, message = match.groups()
                
                # Try to parse message as JSON if it starts with "Request:" or "Response:"
                try:
                    if message.startswith(('Request:', 'Response:')):
                        msg_type, msg_content = message.split(':', 1)
                        data = json.loads(msg_content)
                        preview = f"{msg_type}: {json.dumps(data, indent=2)}"
                    else:
                        preview = message
                        data = None
                except:
                    preview = message
                    data = None
                
                html_content += f"""
                    <div class="log-entry" onclick="toggleDetails(this)">
                        <div class="entry-header">
                            <span class="timestamp">{timestamp}</span>
                            <span class="level level-{level}">{level}</span>
                        </div>
                        <div class="preview">{html.escape(preview)}</div>
                        {"" if not data else f'<div class="details">{json.dumps(data, indent=2)}</div>'}
                    </div>
                """
            else:
                html_content += f"""
                    <div class="log-entry">
                        <div class="preview">{html.escape(line)}</div>
                    </div>
                """
        
        html_content += """
                </div>
            </div>
            <script>
                function toggleDetails(element) {
                    const details = element.querySelector('.details');
                    if (!details) return;
                    
                    const wasHidden = details.style.display === 'none' || !details.style.display;
                    
                    // Hide all other details
                    document.querySelectorAll('.details').forEach(detail => {
                        detail.style.display = 'none';
                    });
                    
                    // Toggle this detail
                    details.style.display = wasHidden ? 'block' : 'none';
                }
                
                function filterLogs() {
                    const searchText = document.getElementById('searchInput').value.toLowerCase();
                    document.querySelectorAll('.log-entry').forEach(entry => {
                        const text = entry.textContent.toLowerCase();
                        entry.style.display = text.includes(searchText) ? 'block' : 'none';
                    });
                }
                
                document.getElementById('searchInput').addEventListener('input', filterLogs);
                
                // Auto-scroll to bottom on load
                window.addEventListener('load', () => {
                    const logViewer = document.querySelector('.log-viewer');
                    logViewer.scrollTop = logViewer.scrollHeight;
                });
            </script>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content, status_code=200)
        
    except Exception as e:
        logging.error(f"Error reading log file '{log_type}': {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error reading log file '{log_type}': {str(e)}"
        )


def get_task_id_from_config():
    code_gen_path = get_codegeneration_path()
    config_path = f"{code_gen_path}/.config.json"
    config_file = Path(config_path)

    # Check if file exists
    
    try:
        if not config_file.exists():
            print(f"Config file not found: {config_path}")
        # Read and parse JSON file
        with config_file.open('r', encoding='utf-8') as file:
            config = json.load(file)
            
        # Validate task_id exists and is not empty
        task_id = config.get('task_id')
        if not task_id:
            print("task_id not found or is empty in config file")
            
        return task_id
        
    except Exception as e:
        print(f"Error while getting getting task id from config file: {e}")


    
def push_to_current_branch():
    task_id = get_task_id_from_config()
    task_type, base_path = find_task_type_and_path(task_id)
    # base_path = f"/tmp/kavia/workspace/code-generation"
    repos = get_all_repos(base_path)
    
    results = []
    original_cwd = os.getcwd()   
    try:
        for repo_name in repos:
                
            repo_path = os.path.join(base_path, repo_name)
            os.chdir(repo_path)
            repo_name = Path(repo_path).name
            logging.info(f"repo_path: {repo_path}, repo_name: {repo_name}")
            
            result = {
                "repo": repo_name,
                "status": "success",
                "current_branch": None,
                "operations": [],
                "error": None
            }
            branch_result = subprocess.run(
            ["git", "branch", "--show-current"],
            capture_output=True,
            text=True,
            check=True
        )
            current_branch = branch_result.stdout.strip()
            logging.info(f"current_branch: {current_branch}")
            result["current_branch"] = current_branch
            if not current_branch:
                print(f"no current branch {current_branch}")
                continue
            
            # Check if there are any changes to commit
            status_result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                check=True
            )
            
            has_changes = bool(status_result.stdout.strip())
            
            if has_changes:
                # Stage all changes
                subprocess.run(["git", "add", "."], check=True)
                result["operations"].append("add")
                
                # Commit changes
                commit_message = f"Auto-commit from task {task_id}"
                subprocess.run(
                    ["git", "commit", "-m", commit_message],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("commit")
                
                # Push current branch
                subprocess.run(
                    ["git", "push", "origin", current_branch],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("push_current")
                results.append(result)
            else:
                print("No changes are made ")
                results.append(result)
            os.chdir(original_cwd)
    except Exception as e:
        print(f"Error ocuured while pushing to current branch: {e}")
    
    return results       
                

def find_task_type_and_path(task_id: str) -> tuple[str, str]:
    
    if os.environ.get("LOCAL_DEBUG"):
        return "code_generation", "/tmp/kavia/workspace/code-generation/"
    """
    Find task type and base path based on task_id by checking running screen sessions
    Returns: (task_type, base_path)
    """
    try:
        sessions = ScreenSessionManager.get_all_sessions()
        
        for session in sessions:
            session_name = session.get("name", "")
            
            # Check for code generation pattern: code-generation-{task_id}-{project_id}
            if session_name.startswith(f"code-generation-{task_id}-") or session_name.startswith(f"cg"):
                return "code_generation", "/home/<USER>/workspace/code-generation/"
            
            # Check for code maintenance pattern: maintenance-{task_id}-{project_id}
            elif session_name.startswith(f"maintenance-{task_id}-") or session_name.startswith(f"cm"):
                return "code_maintenance", f"/home/<USER>/workspace/{task_id}/"
            
            # Check for document creation pattern: document-creation-{project_id}
            elif session_name.startswith("document-creation-") and task_id in session_name:
                return "document_creation", "/home/<USER>/workspace/"
        
        # If no running session found, default to code maintenance
        # (most common case and follows the task_id directory structure)
        logging.warning(f"No running session found for task_id: {task_id}, defaulting to code_maintenance")
        return "code_maintenance", f"/home/<USER>/workspace/{task_id}/"
        
    except Exception as e:
        logging.error(f"Error finding task type for task_id {task_id}: {str(e)}")
        # Default fallback
        return "code_maintenance", f"/home/<USER>/workspace/{task_id}/"

def get_all_repos(base_path: str) -> list[str]:
    """
    Get all repository directories in the base path
    Returns: list of repository directory names
    """
    try:
        base_dir = Path(base_path)
        if not base_dir.exists():
            logging.error(f"Base path does not exist: {base_path}")
            return []
        
        repos = []
        for item in base_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # Check if it's a git repository
                git_dir = item / '.git'
                if git_dir.exists():
                    repos.append(item.name)
                    logging.info(f"Found git repository: {item.name}")
        
        return repos
        
    except Exception as e:
        logging.error(f"Error listing repositories in {base_path}: {str(e)}")
        return []

def process_single_repo(repo_path: str, task_id: str) -> dict:
    """
    Process git operations for a single repository with improved error handling
    and proper handling of non-fast-forward push rejections.
    
    Args:
        repo_path: Path to the git repository
        task_id: Unique identifier for the current task
        
    Returns: 
        dict: Result dictionary with status and operation details
    """
    from pathlib import Path
    import os
    import logging
    import subprocess
    
    repo_name = Path(repo_path).name
    result = {
        "repo": repo_name,
        "status": "success",
        "current_branch": None,
        "operations": [],
        "error": None
    }
    
    original_dir = os.getcwd()
    
    try:
        # Change to repository directory
        os.chdir(repo_path)
        logging.info(f"Processing repository: {repo_path}")
        
        # Get current branch name
        branch_result = subprocess.run(
            ["git", "branch", "--show-current"],
            capture_output=True,
            text=True,
            check=True
        )
        current_branch = branch_result.stdout.strip()
        result["current_branch"] = current_branch
        
        if not current_branch:
            raise Exception("Could not determine current branch")
        
        # Check if there are any changes to commit
        status_result = subprocess.run(
            ["git", "status", "--porcelain"],
            capture_output=True,
            text=True,
            check=True
        )
        
        has_changes = bool(status_result.stdout.strip())
        
        if has_changes:
            # Stage all changes
            subprocess.run(["git", "add", "."], check=True)
            result["operations"].append("add")
            
            # Commit changes
            commit_message = f"Auto-commit from task {task_id}"
            subprocess.run(
                ["git", "commit", "-m", commit_message],
                check=True,
                capture_output=True
            )
            result["operations"].append("commit")
            
            # Push current branch with error handling
            try:
                subprocess.run(
                    ["git", "push", "origin", current_branch],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("push_current")
            except subprocess.CalledProcessError as e:
                if "non-fast-forward" in e.stderr.decode():
                    # Handle non-fast-forward rejection by pulling first
                    logging.info(f"Non-fast-forward rejection detected for {current_branch}, pulling changes first")
                    subprocess.run(
                        ["git", "pull", "--rebase", "origin", current_branch],
                        check=True,
                        capture_output=True
                    )
                    result["operations"].append("pull_rebase_current")
                    
                    # Try pushing again
                    subprocess.run(
                        ["git", "push", "origin", current_branch],
                        check=True,
                        capture_output=True
                    )
                    result["operations"].append("push_current_after_rebase")
                else:
                    # Re-raise if it's a different error
                    raise
        else:
            logging.info(f"No changes to commit in {repo_name}")
        
        # Check if kavia-main branch exists locally
        branch_list_result = subprocess.run(
            ["git", "branch", "--list", "kavia-main"],
            capture_output=True,
            text=True,
            check=True
        )
        
        kavia_main_exists = bool(branch_list_result.stdout.strip())
        
        # Check if kavia-main exists on remote
        remote_branch_exists = False
        try:
            remote_check = subprocess.run(
                ["git", "ls-remote", "--heads", "origin", "kavia-main"],
                capture_output=True,
                text=True,
                check=True
            )
            remote_branch_exists = bool(remote_check.stdout.strip())
        except subprocess.CalledProcessError:
            # Ignore errors when checking remote branch
            pass
            
        if not kavia_main_exists:
            if remote_branch_exists:
                # If branch exists on remote but not locally, track it
                subprocess.run(
                    ["git", "checkout", "--track", "origin/kavia-main"],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("track_remote_kavia_main")
            else:
                # Create kavia-main branch if it doesn't exist anywhere
                subprocess.run(
                    ["git", "checkout", "-b", "kavia-main"],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("create_kavia_main")
        else:
            # Checkout to kavia-main
            subprocess.run(
                ["git", "checkout", "kavia-main"],
                check=True,
                capture_output=True
            )
            result["operations"].append("checkout_kavia_main")
            
            # If remote branch exists, pull latest changes
            if remote_branch_exists:
                try:
                    subprocess.run(
                        ["git", "pull", "--rebase", "origin", "kavia-main"],
                        check=True,
                        capture_output=True
                    )
                    result["operations"].append("pull_rebase_kavia_main")
                except subprocess.CalledProcessError as e:
                    logging.warning(f"Failed to pull kavia-main branch: {e.stderr.decode() if e.stderr else str(e)}")
                    # Continue anyway - we'll try to merge and push
        
        # Merge current branch to kavia-main
        if current_branch != "kavia-main":
            subprocess.run(
                ["git", "merge", current_branch],
                check=True,
                capture_output=True
            )
            result["operations"].append("merge")
        
        # Push kavia-main with error handling
        try:
            subprocess.run(
                ["git", "push", "origin", "kavia-main"],
                check=True,
                capture_output=True
            )
            result["operations"].append("push_kavia_main")
        except subprocess.CalledProcessError as e:
            if "non-fast-forward" in e.stderr.decode():
                # Handle non-fast-forward rejection with force-with-lease
                logging.warning("Non-fast-forward rejection when pushing kavia-main. Using force-with-lease.")
                subprocess.run(
                    ["git", "push", "--force-with-lease", "origin", "kavia-main"],
                    check=True,
                    capture_output=True
                )
                result["operations"].append("push_kavia_main_force_with_lease")
            else:
                # Re-raise if it's a different error
                raise
        
        logging.info(f"Successfully processed repository: {repo_name}")
        
    except subprocess.CalledProcessError as e:
        error_msg = f"Git command failed: {e.stderr.decode() if e.stderr else str(e)}"
        logging.error(f"Error processing {repo_name}: {error_msg}")
        result["status"] = "error"
        result["error"] = error_msg
        
    except Exception as e:
        error_msg = str(e)
        logging.error(f"Error processing {repo_name}: {error_msg}")
        result["status"] = "error"
        result["error"] = error_msg
    
    finally:
        # Always return to the original directory
        os.chdir(original_dir)
    
    return result

class MergeResponse(BaseModel):
    message: str
    task_type: str
    base_path: str
    processed_repos: list[str]
    results: list[dict]

@app.post("/merge", response_model=MergeResponse)
async def merge_repos(
    task_id: str = Query(..., min_length=1, description="Task ID to find and merge repositories"),
    project_id: int = Query(..., ge=1, description="project id"),
    tenant_id: str = Query(..., min_lenght=1, description='Tenant id')
):
    """
    Merge all repositories for a given task_id.
    Commits current changes and merges current branch to kavia-main for all repos.
    """
    try:
        # Find task type and base path
        task_type, base_path = find_task_type_and_path(task_id)
        logging.info(f"Task {task_id} identified as {task_type} with base path: {base_path}")
        
        tenant_context.set(tenant_id)
        
        print(type(project_id))
        result = await get_project_paths(project_id=project_id)
        print(get_tenant_id())
        print("Result :", result)
        for path_info in result["paths"]:
            print("Path Info")
            print(path_info)
            source_path = path_info["source_path"]
            destination_path = path_info["destination_path"]
            do_sync(source_path,destination_path )
        
        
        # Get all repositories in the base path
        repos = get_all_repos(base_path)
        
        if not repos:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No git repositories found in {base_path}"
            )
        
        logging.info(f"Found {len(repos)} repositories to process: {repos}")
        
        # Process each repository
        results = []
        processed_repos = []
        
        original_cwd = os.getcwd()
        
        try:
            for repo_name in repos:
                repo_path = os.path.join(base_path, repo_name)
                result = process_single_repo(repo_path, task_id)
                results.append(result)
                processed_repos.append(repo_name)
        finally:
            # Restore original working directory
            os.chdir(original_cwd)
        
        # Count successful operations
        successful_repos = [r for r in results if r["status"] == "success"]
        failed_repos = [r for r in results if r["status"] == "error"]
        
        message = f"Processed {len(repos)} repositories. "
        message += f"Success: {len(successful_repos)}, Failed: {len(failed_repos)}"
        
        if failed_repos:
            message += f". Failed repos: {[r['repo'] for r in failed_repos]}"
        
        logging.info(f"Merge operation completed: {message}")
        
        return MergeResponse(
            message=message,
            task_type=task_type,
            base_path=base_path,
            processed_repos=processed_repos,
            results=results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in merge operation for task_id {task_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing merge operation: {str(e)}"
        )
