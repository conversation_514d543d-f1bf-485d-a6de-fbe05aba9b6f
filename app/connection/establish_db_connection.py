from app.classes.NodeDB  import NodeDB
from app.classes.VectorDB import <PERSON><PERSON><PERSON>b<PERSON>and<PERSON>
from app.classes.MongoDB import <PERSON>goDB<PERSON><PERSON><PERSON>
from typing import Optional, Dict, Type, Any
from app.classes.InspectorDB import InspectorDB
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id, KAVIA_ROOT_DB_NAME
from app.repository.mongodb.user_repository import UserRepository
from app.repository.mongodb.base_repository import BaseMongoRepository
from app.repository.mongodb.task_repository import TaskRepository
from app.repository.mongodb.notification_repository import NotificationRepository
from app.repository.mongodb.repositories import LLMCostsRepository, ConfigRepository, CodeGenTasksRepository
from app.repository.mongodb.tenant_repository import TenantCredentialsRepository, TenantPermissionRepository
from app.repository.mongodb.device_token_repository import DeviceTokenRepository
from app.utils.b2c_utils import get_collection_name


db: Any = None
vector_db: Any = None
mongo_db: Any = None
mongo_db_collection: Dict[str, MongoDBHandler] = {}
inspector_db: Any = None



class TenantDBManager:
    def __init__(self):
        self.settings = settings
        self.db_connections: Dict[str, NodeDB] = {}
        
    def get_connection(self, tenant_id: str = None) -> NodeDB:
        """Get or create a database connection for a tenant"""
        # Use tenant_id as database name, fallback to 'neo4j' if not provided
        database_name = tenant_id if tenant_id else "neo4j"
        
        if database_name not in self.db_connections:
            self.db_connections[database_name] = NodeDB(
                uri=self.settings.NEO4J_CONNECTION_URI,
                user=self.settings.NEO4J_USER,
                password=self.settings.NEO4J_PASSWORD,
                database=database_name
            )
        
        return self.db_connections[database_name]

# Create global instance
tenant_db_manager = TenantDBManager()

def connect_node_db() -> NodeDB:
    """Connect to the NodeDB."""
    global db
    db = NodeDB(settings.NEO4J_CONNECTION_URI,settings.NEO4J_USER,settings.NEO4J_PASSWORD)
    return db

def connect_inspector_db() -> InspectorDB:
    """Connect to the NodeDB."""
    global inspector_db
    inspector_db= InspectorDB(settings.CODEINSPECTOR_NEO4J_CONNECTION_URI,settings.CODEINSPECTOR_NEO4J_USER,settings.CODEINSPECTOR_NEO4J_PASSWORD)
    return inspector_db

def connect_vector_db():
    """Connect to the VectorDB."""
    global vector_db
    vector_db = VectorDbHandler(settings.MONGO_CONNECTION_URI,settings.MONGO_DB_NAME)
    return vector_db

def connect_mongo_db(db_name, collection_name, user_id=None)-> MongoDBHandler:
    """Connect to the MongoDB."""
    if collection_name in ['tenant_permissions', 'tenant_users', 'tenant_groups', 'tenant_organizations','tenant_plans','public_projects']:
        db_name = KAVIA_ROOT_DB_NAME
    else:
        collection_name = get_collection_name(db_name, collection_name, user_id)

    global mongo_db
    mongo_db = MongoDBHandler(settings.MONGO_CONNECTION_URI, db_name, collection_name, user_id)
    return mongo_db

def connect_git_mongo_db(db_name, collection_name)-> MongoDBHandler:
    """Connect to the MongoDB."""
             
    global mongo_db
    mongo_db = MongoDBHandler(settings.MONGO_CONNECTION_URI, db_name, collection_name )

    return mongo_db


def get_node_db(tenant_id:str = None) -> NodeDB:
    """Get NodeDB instance based on tenant context"""
    try:
        if tenant_id:
            tenant_id = f"{settings.STAGE.replace('-','').replace('_','').replace(' ','')}{tenant_id}"
        else:
            tenant_id = get_tenant_id()
            tenant_id = f"{settings.STAGE.replace('-','').replace('_','').replace(' ','')}{tenant_id}"
        print(f"Getting NodeDB connection for tenant: {tenant_id}")  # Debug print
        return tenant_db_manager.get_connection(tenant_id)
    except Exception as e:
        print(f"Error getting tenant database: {str(e)}")
        return tenant_db_manager.get_connection(settings.KAVIA_ROOT_TENANT_ID)

def get_inspector_db() -> InspectorDB:
    """Get the NodeDB instance for graph view"""
    if inspector_db is None:
        connect_inspector_db()
    return inspector_db

def get_vector_db():
    """Get the VectorDB instance."""
    if vector_db is None:
        connect_vector_db()
    return vector_db

def get_mongo_db(db_name:str = settings.MONGO_DB_NAME, collection_name:str = 'tasks', user_id=None) -> MongoDBHandler:
    """Get the MongoDB instance."""
    return connect_mongo_db(db_name, collection_name, user_id)

class MongoDBConnection:
    _repositories: Dict[str, Type[BaseMongoRepository]] = {
        "user": UserRepository,
        "notification": NotificationRepository,
        "task": TaskRepository,
        "config": ConfigRepository,  
        "llm_costs": LLMCostsRepository,
        "code_gen_tasks": CodeGenTasksRepository,
        "tenant_credentials": TenantCredentialsRepository,
        "tenant_permissions": TenantPermissionRepository,
        "device_token": DeviceTokenRepository
    }

    @classmethod
    async def get_repository(cls, repo_type: str, table_name: Optional[str] = None):
        if repo_type not in cls._repositories:
            raise ValueError(f"Unknown repository type: {repo_type}")
        
        # Create a new instance each time instead of caching
        if table_name:
            return cls._repositories[repo_type](table_name)
        return cls._repositories[repo_type]()
    
async def get_mongo_db_v1(repo_type: str = "user", table_name: Optional[str] = None):
    """Get MongoDB repository instance with tenant isolation."""
    return await MongoDBConnection.get_repository(repo_type, table_name)

