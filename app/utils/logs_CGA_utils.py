import os
import configparser
def get_config_dir(tenant_id,project_id,task_id):

    if os.getenv("LOCAL_DEBUG"):
        return f"/tmp/{tenant_id}/{project_id}/logs/{task_id}/"
    else:
        return f"/efs/{tenant_id}/{project_id}/logs/{task_id}/"

def update_config_dir(config_path: str,tenant_id,project_id,task_id) -> None:
    """
    Updates the config_dir in the config.ini file using the path from get_config_dir()
    
    Args:
        config_path: Path to the config.ini file
    """
    try:
        #TENANT ID OVERRIDE
        tenant_id = "T0000"
        # Get the config directory path from the external function
        config_dir_path = get_config_dir(tenant_id,project_id,task_id)
        
        # Create parent directory for config file if it doesn't exist
        config_file_dir = os.path.dirname(config_path)
        if config_file_dir:
            os.makedirs(config_file_dir, exist_ok=True)
            
        # Create the actual config directory
        os.makedirs(config_dir_path, exist_ok=True)
        
        # Initialize config parser
        config = configparser.ConfigParser()
        
        # Read existing config if it exists
        if os.path.exists(config_path):
            config.read(config_path)
        
        # Create SYSTEM section if it doesn't exist
        if 'SYSTEM' not in config:
            config['SYSTEM'] = {}
            
        # Update the config_dir value
        config['SYSTEM']['config_dir'] = config_dir_path
        
        # Write the updated config back to the file
        with open(config_path, 'w') as configfile:
            config.write(configfile)
            
        print(f"Successfully updated config_dir to: {config_dir_path}")
    except Exception as e:
        print(f"Error updating config_dir in {config_path}: {str(e)}")
        import traceback
        print(traceback.format_exc())