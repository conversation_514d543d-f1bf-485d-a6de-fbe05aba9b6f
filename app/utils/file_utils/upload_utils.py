# app/utils/file_utils/upload_utils.py

import boto3
import os
import shutil
from app.core.Settings import settings
from app.utils.project_utils import get_stage
from botocore.exceptions import ClientError
import json
from io import BytesIO
import uuid
import urllib.parse

# Initialize boto3 clients
s3_client = boto3.client('s3',
                         aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                         aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                         region_name=settings.AWS_REGION
                         )

s3_resource = boto3.resource('s3',
                             aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                             region_name=settings.AWS_REGION
                             )


def get_clean_stage_name(stage_name: str) -> str:
    """Clean stage name by removing underscores and converting to lowercase"""
    return stage_name.replace('_', '').lower()


def generate_bucket_name(tenant_id: str) -> str:
    """Generate bucket name based on stage and tenant ID"""
    clean_stage = get_clean_stage_name(get_stage(settings=settings))
    return f"kavia-attachments-{clean_stage}-{tenant_id}".lower()


def get_tenant_bucket_for_file(tenant_id: str) -> str:

    clean_stage = get_clean_stage_name(get_stage(settings=settings))
    bucket_name = f"ingested-documents-collection-{clean_stage}-{tenant_id}".lower(
    )
    print(bucket_name)
    bucket_name = bucket_name.replace('_', '-').replace('/', '-')
    print(bucket_name)
    if ensure_bucket_exists(bucket_name):
        return bucket_name
    raise ValueError(
        f"Could not access or create bucket for tenant {tenant_id}")


def ensure_bucket_exists(bucket_name: str) -> bool:
    """
    Ensure bucket exists, create if it doesn't
    Returns True if bucket exists or was created successfully
    """
    try:
        s3_client.head_bucket(Bucket=bucket_name)
        return True
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code')
        if error_code == '404' or error_code == 'NoSuchBucket':
            try:
                # Create bucket with private access
                if settings.AWS_REGION == 'us-east-1':
                    s3_client.create_bucket(
                        Bucket=bucket_name,
                        ACL='private'
                    )
                else:
                    s3_client.create_bucket(
                        Bucket=bucket_name,
                        ACL='private',
                        CreateBucketConfiguration={
                            'LocationConstraint': settings.AWS_REGION
                        }
                    )

                # Enable versioning
                s3_client.put_bucket_versioning(
                    Bucket=bucket_name,
                    VersioningConfiguration={'Status': 'Enabled'}
                )

                # Add bucket encryption
                s3_client.put_bucket_encryption(
                    Bucket=bucket_name,
                    ServerSideEncryptionConfiguration={
                        'Rules': [
                            {
                                'ApplyServerSideEncryptionByDefault': {
                                    'SSEAlgorithm': 'AES256'
                                }
                            }
                        ]
                    }
                )

                # Add lifecycle rules with correct format
                lifecycle_config = {
                    'Rules': [
                        {
                            'ID': 'TransitionToIA',
                            'Status': 'Enabled',
                            'Prefix': '',  # Apply to all objects
                            'Transitions': [
                                {
                                    'Days': 90,
                                    'StorageClass': 'STANDARD_IA'
                                }
                            ],
                            'NoncurrentVersionTransitions': [
                                {
                                    'NoncurrentDays': 90,
                                    'StorageClass': 'STANDARD_IA'
                                }
                            ]
                        }
                    ]
                }

                s3_client.put_bucket_lifecycle_configuration(
                    Bucket=bucket_name,
                    LifecycleConfiguration=lifecycle_config
                )


                print(f"Successfully created bucket: {bucket_name}")
                return True

            except ClientError as create_error:
                error_msg = str(create_error)
                print(f"Error creating bucket: {error_msg}")
                if "AccessDenied" in error_msg:
                    print("Please check AWS credentials and permissions")
                return False
        return False


def get_tenant_bucket(tenant_id: str) -> str:
    try:
        """Get or create tenant-specific bucket"""
        bucket_name = generate_bucket_name(tenant_id)
        
        print(bucket_name)
        bucket_name = bucket_name.replace('_', '-').replace('/', '-')
        print(bucket_name)
        
        if ensure_bucket_exists(bucket_name):
            return bucket_name
        raise ValueError(
            f"Could not access or create bucket for tenant {tenant_id}")

    except Exception as e:
        print('error in get_tenant_bucket:', e)

def create_thumbnail(image, size=(400, 400)):
    """Create thumbnail from image"""
    image.thumbnail(size)
    return image


def generate_presigned_url(bucket: str, key: str, expiration: int = 43200):
    """Generate presigned URL for S3 object"""
    try:
        url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket, 'Key': key},
            ExpiresIn=expiration
        )

        if key.lower().endswith('.pdf'):
            parsed = urllib.parse.urlparse(url)
            # Quote the entire path but preserve forward slashes
            encoded_path = urllib.parse.quote(parsed.path, safe='/')
            fixed_url = urllib.parse.urlunparse(parsed._replace(path=encoded_path))
            return fixed_url

        return url

    except ClientError as e:
        print(f"Error generating presigned URL: {str(e)}")
        return None

# In upload_utils.py, add this new function:


def generate_document_presigned_url(bucket: str, key: str, disposition: str = 'inline', expiration: int = 43200):
    """Generate presigned URL specifically for document viewing/downloading"""
    try:
        params = {
            'Bucket': bucket,
            'Key': key,
            'ResponseContentDisposition': disposition
        }

        url = s3_client.generate_presigned_url(
            'get_object',
            Params=params,
            ExpiresIn=expiration
        )

        if key.lower().endswith('.pdf'):
            parsed = urllib.parse.urlparse(url)
            # Quote the entire path but preserve forward slashes
            encoded_path = urllib.parse.quote(parsed.path, safe='/')
            fixed_url = urllib.parse.urlunparse(parsed._replace(path=encoded_path))
            return fixed_url

        return url

    except ClientError as e:
        print(f"Error generating document presigned URL: {str(e)}")
        return None

def sync_to_s3(save_path: str, tenant_id: str, file_id: str, folder_name: str):
    """Sync files to tenant-specific S3 bucket"""
    if not os.path.isdir(save_path):
        raise ValueError(f'Directory {save_path} not found.')

    bucket = get_tenant_bucket(tenant_id)

    for filename in os.listdir(save_path):
        file_path = os.path.join(save_path, filename)
        s3_key = f"{folder_name}/{file_id}/{filename}"

        try:
            s3_client.upload_file(file_path, bucket, s3_key)
        except Exception as e:
            print(f"Failed to upload file {filename}: {str(e)}")
            raise


def upload_and_process(identifier: str, file_content: bytes, file_name: str,
                       content_type: str, tenant_id: str, folder_name: str = "attachments"):
    """Upload and process file for tenant"""
    try:
        # Create a temporary directory
        save_path = f"/tmp/{identifier}/{str(uuid.uuid4())}"
        os.makedirs(save_path, exist_ok=True)

        # Write file content
        file_path = os.path.join(save_path, file_name)
        with open(file_path, 'wb') as f:
            f.write(file_content)

        # Get tenant bucket and sync file

        bucket_name = get_tenant_bucket(tenant_id)
        sync_to_s3(save_path, tenant_id, identifier, folder_name)

        # Construct S3 location
        s3_location = f"{bucket_name}/{folder_name}/{identifier}/{file_name}"

        # Return attachment data
        return {
            "file_name": file_name,
            "content_type": content_type,
            "s3_location": s3_location,
            "bucket_name": bucket_name
        }

    except Exception as e:
        print(f"Error processing file: {str(e)}")
        raise

    finally:
        print(save_path)
        # Clean up temporary directory
        shutil.rmtree(save_path, ignore_errors=True)

        # Validate if cleared
        if validate_directory_cleared(save_path):
            print(f"Directory '{save_path}' was successfully cleared or removed")
        else:
            print(f"Directory '{save_path}' still exists and contains files")

def validate_directory_cleared(path):
    # Check if directory exists
    if not os.path.exists(path):
        # If path doesn't exist, rmtree completely removed it
        return True
    
    # Check if directory is empty (no files or subdirectories)
    return len(os.listdir(path)) == 0
