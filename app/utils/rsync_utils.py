import os
import subprocess

import os
import shutil
from pathlib import Path

def copy_from_multiple_sources(source_paths, target_path, log_prefix=""):
    """
    Copy files from multiple source directories to a single target directory,
    creating numbered copies for duplicate filenames.
    
    Args:
        source_paths (list): List of source directory paths
        target_path (str): Target directory path
        log_prefix (str): Prefix for log messages
        
    Returns:
        bool: True if copy was successful, False otherwise
    """
    try:
        # Create the target directory if it doesn't exist
        os.makedirs(target_path, exist_ok=True)
        
        # Track overall success
        overall_success = True
        files_copied = 0
        
        # Process each source directory
        for source_path in source_paths:
            print(f"{log_prefix}Processing source directory: {source_path}")
            
            if not os.path.exists(source_path):
                print(f"{log_prefix}Warning: Source path {source_path} does not exist, skipping.")
                continue
                
            # Get all files in the source directory (including in subdirectories)
            for root, _, files in os.walk(source_path):
                for filename in files:
                    source_file = os.path.join(root, filename)
                    
                    # Calculate relative path from source root
                    rel_path = os.path.relpath(source_file, source_path)
                    target_file = os.path.join(target_path, rel_path)
                    
                    # Ensure target subdirectory exists
                    os.makedirs(os.path.dirname(target_file), exist_ok=True)
                    
                    # Handle filename conflicts
                    if os.path.exists(target_file):
                        base_name, extension = os.path.splitext(target_file)
                        counter = 1
                        
                        # First try with _copy suffix
                        new_target = f"{base_name}_copy{extension}"
                        
                        # If that also exists, start incrementing numbers
                        while os.path.exists(new_target):
                            counter += 1
                            new_target = f"{base_name}_copy_{counter}{extension}"
                        
                        target_file = new_target
                    
                    try:
                        # Copy the file, preserving metadata
                        shutil.copy2(source_file, target_file)
                        files_copied += 1
                        print(f"{log_prefix}Copied: {source_file} -> {target_file}")
                    except Exception as e:
                        print(f"{log_prefix}Error copying {source_file}: {str(e)}")
                        overall_success = False
        
        print(f"{log_prefix}Copy operation completed. {files_copied} files copied.")
        return overall_success
    
    except Exception as e:
        print(f"{log_prefix}Error during copy operation: {str(e)}")
        return False

import os
import subprocess
import logging
import json
from pathlib import Path
import time
import threading

def sync_workspace_to_efs(project_id, env_name="beta", tenant_id="T0000"):
    """
    Sync workspace files to EFS storage before pod deletion.
    
    Args:
        project_id (str): Project ID for the deployment
        env_name (str): Environment name (e.g., 'beta', 'dev')
        tenant_id (str): Tenant ID for the workspace
        
    Returns:
        bool: True if sync was successful, False otherwise
    """
    try:
        local_workspace = "/home/<USER>/workspace"
        efs_mount = "/efs"
        efs_path = f"{efs_mount}/{tenant_id}/{project_id}/workspace"
        
        logging.info(f"Starting workspace backup to EFS for project {project_id}")
        
        # Check if EFS is mounted
        result = subprocess.run(
            ["mountpoint", "-q", efs_mount],
            capture_output=True
        )
        
        if result.returncode != 0:
            logging.error(f"EFS is not mounted at {efs_mount}, cannot backup workspace")
            return False
            
        # Create EFS directory structure if it doesn't exist
        os.makedirs(f"{efs_mount}/{tenant_id}", exist_ok=True)
        os.makedirs(f"{efs_mount}/{tenant_id}/{project_id}", exist_ok=True)
        os.makedirs(efs_path, exist_ok=True)
        
        # Set backup flag file to indicate backup is in progress
        backup_flag_file = Path("/tmp/workspace_backup_in_progress.flag")
        backup_flag_file.write_text(f"{project_id}:{env_name}:{time.time()}")
        
        # Define exclude patterns for rsync
        exclude_patterns = [
            "--exclude", "node_modules",
            "--exclude", ".npm",
            "--exclude", ".yarn", 
            "--exclude", "dist",
            "--exclude", "build",
            "--exclude", ".next",
            "--exclude", "*.pyc",
            "--exclude", "__pycache__",
            "--exclude", ".pytest_cache",
            "--exclude", ".venv",
            "--exclude", "venv",
            "--exclude", "env"
        ]
        
        # Run rsync to backup workspace to EFS
        rsync_cmd = ["rsync", "-av"] + exclude_patterns + [f"{local_workspace}/", f"{efs_path}/"]
        logging.info(f"Running rsync command: {' '.join(rsync_cmd)}")
        
        result = subprocess.run(
            rsync_cmd,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            logging.error(f"Failed to sync workspace to EFS: {result.stderr}")
            backup_flag_file.unlink(missing_ok=True)
            return False
            
        logging.info(f"Workspace backup completed successfully: {result.stdout[:200]}...")
        
        # Set completed flag file
        backup_completed_file = Path("/tmp/workspace_backup_completed.flag")
        backup_completed_file.write_text(f"{project_id}:{env_name}:{time.time()}")
        
        # Remove the in-progress flag
        backup_flag_file.unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        logging.error(f"Error during workspace backup: {str(e)}")
        # Clean up flag file if exists
        Path("/tmp/workspace_backup_in_progress.flag").unlink(missing_ok=True)
        return False

def restore_workspace_from_efs(project_id, env_name="beta", tenant_id="T0000"):
    """
    Restore workspace files from EFS storage when service starts.
    
    Args:
        project_id (str): Project ID for the deployment
        env_name (str): Environment name (e.g., 'beta', 'dev')
        tenant_id (str): Tenant ID for the workspace
        
    Returns:
        bool: True if restore was successful, False otherwise
    """
    try:
        local_workspace = "/home/<USER>/workspace"
        efs_mount = "/efs"
        efs_path = f"{efs_mount}/{tenant_id}/{project_id}/workspace"
        
        logging.info(f"Starting workspace restoration from EFS for project {project_id}")
        
        # Check if EFS is mounted
        result = subprocess.run(
            ["mountpoint", "-q", efs_mount],
            capture_output=True
        )
        
        if result.returncode != 0:
            logging.error(f"EFS is not mounted at {efs_mount}, cannot restore workspace")
            return False
            
        # Check if EFS path exists
        if not os.path.exists(efs_path):
            logging.warning(f"EFS path {efs_path} does not exist, no backup to restore")
            return False
            
        # Create local workspace if it doesn't exist
        os.makedirs(local_workspace, exist_ok=True)
        
        # Set restore flag file
        restore_flag_file = Path("/tmp/workspace_restore_in_progress.flag")
        restore_flag_file.write_text(f"{project_id}:{env_name}:{time.time()}")
        
        # Define exclude patterns for rsync
        exclude_patterns = [
            "--exclude", "node_modules",
            "--exclude", ".npm",
            "--exclude", ".yarn", 
            "--exclude", "dist",
            "--exclude", "build",
            "--exclude", ".next",
            "--exclude", "*.pyc",
            "--exclude", "__pycache__",
            "--exclude", ".pytest_cache",
            "--exclude", ".venv",
            "--exclude", "venv",
            "--exclude", "env"
        ]
        
        # Run rsync to restore workspace from EFS
        rsync_cmd = ["rsync", "-av"] + exclude_patterns + [f"{efs_path}/", f"{local_workspace}/"]
        logging.info(f"Running rsync command: {' '.join(rsync_cmd)}")
        
        result = subprocess.run(
            rsync_cmd,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            logging.error(f"Failed to restore workspace from EFS: {result.stderr}")
            restore_flag_file.unlink(missing_ok=True)
            return False
            
        logging.info(f"Workspace restoration completed successfully: {result.stdout[:200]}...")
        
        # Set completed flag file
        restore_completed_file = Path("/tmp/workspace_restore_completed.flag")
        restore_completed_file.write_text(f"{project_id}:{env_name}:{time.time()}")
        
        # Remove the in-progress flag
        restore_flag_file.unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        logging.error(f"Error during workspace restoration: {str(e)}")
        # Clean up flag file if exists
        Path("/tmp/workspace_restore_in_progress.flag").unlink(missing_ok=True)
        return False
    
def rsync_directories(source_path, target_path, delete=True, log_prefix=""):
    """
    Sync two directories using rsync
    
    Args:
        source_path (str): Source directory path
        target_path (str): Target directory path
        delete (bool): Whether to delete files in target that don't exist in source
        log_prefix (str): Prefix for log messages
        
    Returns:
        bool: True if sync was successful, False otherwise
    """
    try:
        # Create the target directory if it doesn't exist
        if not os.path.exists(target_path):
            os.makedirs(target_path, exist_ok=True)
        
        # Ensure source path ends with slash for rsync
        if not source_path.endswith('/'):
            source_path += '/'

        # Run rsync with appropriate options
        # -a: archive mode (preserves permissions, etc.)
        # -v: verbose
        # -z: compression
        # --delete: delete files in target that are not in source
        
        # Build rsync command
        rsync_command = ["rsync", "-avz"]
        if delete:
            rsync_command.append("--delete")
        rsync_command.extend([source_path, target_path])
        
        # Execute rsync
        print(f"{log_prefix}Executing: {' '.join(rsync_command)}")
        process = subprocess.run(
            rsync_command,
            capture_output=True,
            text=True
        )
        
        # Check result
        if process.returncode != 0:
            print(f"{log_prefix}Rsync error: {process.stderr}")
            return False
        
        print(f"{log_prefix}Successfully synced from {source_path} to {target_path}")
        # Print a summary of what was transferred (extract from stdout)
        summary_lines = [line for line in process.stdout.split('\n') if 'sent' in line or 'total size' in line]
        for line in summary_lines:
            print(f"{log_prefix}Rsync summary: {line}")
            
        return True
    
    except Exception as e:
        print(f"{log_prefix}Error during rsync operation: {str(e)}")
        return False