
#app.utils.batch_utils.py
import asyncio
import os
import json
import threading

from pymongo import MongoClient
from app.connection.establish_db_connection import get_node_db, get_mongo_db, NodeDB,MongoDBHandler
from app.utils.respository_utils import create_repository_in_workspace
from app.utils.datetime_utils import generate_timestamp
from app.utils.hash import decrypt_string
from app.utils.respository_utils import _run_clone_in_background
from app.models.scm import ACCESS_TOKEN_PATH
from app.core.Settings import settings
from app.connection.tenant_middleware import get_tenant_id
from app.utils.node_utils import get_node_type
from app.connection.tenant_middleware import get_user_id
from code_generation_core_agent.agents.project_welcome_page import  ContainerType
from app.utils.code_generation_utils import convert_manifest_to_yaml_string, get_container_type
from code_generation_core_agent.project_schemas import  dict_to_project_schema
from app.utils.respository_utils import _run_clone_in_background
from fastapi import HTTPException

async def task_execute_maintenance(project_id: int, db: NodeDB = None):
    if not db:
        db = get_node_db()
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    
    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        raise HTTPException(status_code=404, detail="Project not configured")
    
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    
    containers = await db.get_nodes_connected_by_multiple_hops(project_id, "HAS_CHILD", "Container", 5)
    
    work_items = {
        "project_id": project_id,
        "project_name": project_details.get("Title"),
        "containers": []
    }

    for container in containers:
        container_id = str(container.get("id"))
        
        # Handle repository creation/verification
        if container_id not in project_repositories:
            try:
                repository_response = await create_repository_in_workspace(
                    project_id=project_id,
                    container_id=int(container_id),
                    db=db
                )
                
                repository_details = repository_response.get("repository")
                project_repositories[container_id] = repository_details
                
            except Exception as e:
                print(generate_timestamp(),f"Error creating repository for container {container_id}: {str(e)}")
                continue
        
        components = await db.get_child_nodes(int(container_id), "Architecture")
        contanier_details = {
            "name": container.get("properties", {}).get("Title"),
            "description": container.get("properties", {}).get("Description"),
            "repository": project_repositories.get(container_id),
            "components": {}
        }


        for component in components:
            component_id = str(component.get("id"))
            component_info = await db.get_work_items(int(component_id))
            
            if component_info:
                contanier_details["components"][component_id] = {
                    "name": component_info.get("component_name"),
                    "description": component_info.get("description"),
                    "repository_name": component_info.get("repository_name"),
                    "root_folder": component_info.get("root_folder"),
                    "design": component_info.get("design", {}),
                    "algorithms": component_info.get("Algorithm", []),
                    "pseudocode": component_info.get("Pseudocode", [])
                }
        work_items["containers"].append(contanier_details)

    project_details["repositories"] = json.dumps(project_repositories)

    return project_details, work_items

async def task_execute(project_id: int, container_ids: list, db: NodeDB = None, test_case: bool = False, mongo_db: MongoClient = None):
    from app.routes.repository_route import create_github_repository, SCMConfiguration, name_to_slug
    if not db:
        db = get_node_db()
        
    if not mongo_db:
        mongo_db = get_mongo_db().db
    
    project = await db.get_node_by_label_id(project_id, "Project")
    project_details = project.get("properties")
    


    if not project_details or get_node_type(project_details.get("Type")) != "Project":
        print(generate_timestamp(),"Project not configured")
        raise HTTPException(status_code=404, detail="Project not configured")
    
    # Load project repositories
    project_repositories = json.loads(project_details.get("repositories", "{}"))
    print(generate_timestamp(),"container_ids", container_ids)
    print(generate_timestamp(),project_repositories)
    project_details["current_repositories"] = []
    # Check if container repository exists, if not create one
    container_details_hash_map = {}
    async def process_container(container_id):
        container_node = await db.get_node_by_id(container_id)
        container_details = container_node.get("properties")
        
        container_repository = project_repositories.get(str(container_id))
        container_details_hash_map[str(container_id)] = container_details
        if not container_repository:
            try:
                if not os.getenv("encrypted_scm_id"):
                    repository_response = await create_repository_in_workspace(
                        project_id=project_id,
                        container_id=container_id,
                        db=db
                    )
                else: 


                    encrypted_scm_id = os.getenv("encrypted_scm_id")
                    scm_id = decrypt_string(encrypted_scm_id)
                    config = mongo_db["scm_configurations"].find_one({"scm_id": scm_id})
                    config['encrypted_scm_id'] = encrypted_scm_id
                    scm_config = SCMConfiguration(**config) if config else None
                    repository_name = name_to_slug(
                        f"{project_details.get('Title', project_details.get('Name'))}"
                    )
                    repository_name = f'{repository_name}-{container_id}'
                    repository_response = await create_github_repository(repository_name, config=scm_config, is_private=False)
                    print(generate_timestamp(),"Repository Response from SCM :", repository_response)
                    from app.routes.kg_route import CodebaseImportRequest, RepoBranchRequest, import_codebase
                    # Prepare repository data for import_codebase
                    repo_request = RepoBranchRequest(
                        repo_name=f"{repository_response['organization']}/{repository_response['repositoryName']}",
                        branch_name=repository_response.get('default_branch', 'kavia-main'),
                        repo_type="public",
                        repo_id=str(repository_response['repositoryId']),
                        associated=True,
                        encrypted_scm_id=encrypted_scm_id
                    )
                    
                    # Create CodebaseImportRequest
                    import_request = CodebaseImportRequest(
                        project_id=project_id,
                        repositories=[repo_request],
                        encrypted_scm_id=encrypted_scm_id
                    )
                    
                    # Call the import_codebase function
                    mock_current_user = {"cognito:username": get_user_id()} 
                    import_result = await import_codebase(
                        request=import_request,
                        upstream=False,
                        ignore_build=True,
                        current_user=mock_current_user
                    )
                    print(generate_timestamp(),import_result)
                    print(generate_timestamp(),f"Codebase import initiated for repository: {repository_response['repositoryName']}")
        
                repository_response["container_id"] = str(container_id)
                repository_response["container_name"] = str(container_details.get("Title"))
                
                return {
                    "container_id": str(container_id),
                    "repository_data": repository_response,
                    "is_new": True
                }
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=str(e))
        else:
            container_repository["container_id"] = str(container_id)
            container_repository["container_name"] = str(container_details.get("Title"))
            repository_metadata = container_repository.copy()
                            
                # Handle access token based on token path
            if repository_metadata.get("access_token_path") == ACCESS_TOKEN_PATH.KAVIA_MANANGED.value:
                access_token = repository_metadata.get("access_token")
                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                    access_token = settings.GITHUB_ACCESS_TOKEN
                repository_metadata["access_token"] = access_token

            elif repository_metadata.get("encrypted_scm_id") or repository_metadata.get("scm_id"):
                if repository_metadata.get("encrypted_scm_id"):
                    scm_id = repository_metadata.get("encrypted_scm_id")
                    decrypted_scm_id = decrypt_string(scm_id)
                    scm_id = decrypted_scm_id
                else:
                    decrypted_scm_id = repository_metadata.get("scm_id")
                    scm_id = decrypted_scm_id
                config = mongo_db["scm_configurations"].find_one({"scm_id": scm_id})
                config['encrypted_scm_id'] = repository_metadata.get("encrypted_scm_id")
                scm_config = SCMConfiguration(**config) if config else None
                if not config:
                    raise Exception(f"Failed to get SCM configuration for container {container_id}")
                access_token = scm_config.credentials.access_token
                if not access_token or repository_metadata.get("organization") == settings.GITHUB_DEFAULT_ORGANIZATION:
                    access_token = settings.GITHUB_ACCESS_TOKEN
                repository_metadata["access_token"] = access_token
                
            tenant_id = get_tenant_id()
            task_id = os.environ.get("task_id")
            clone_thread = threading.Thread(
                target=_run_clone_in_background,
                args=(
                    repository_metadata,
                    tenant_id,
                    task_id,
                    repository_metadata["repositoryName"]
                ),
                daemon=True
            )
            clone_thread.start()
            print(generate_timestamp(),generate_timestamp(), f"Started background clone thread for repository: {repository_metadata['repositoryName']}")

                
        
        container_details["repository"] = container_repository
        return {
            "container_id": str(container_id),
            "repository_data": container_repository,
            "is_new": False
        }

    # Process all containers in parallel
    container_results = await asyncio.gather(*[process_container(container_id) for container_id in container_ids])
    ports_to_map = {
        "frontend": settings.FRONTEND_PORT,
        "backend": settings.BACKEND_PORT,
        "database": settings.DATABASE_PORT,
    }
       
    
    
    def get_container_port(container):
        container_type = get_container_type(container)
        if container_type == ContainerType.FRONTEND.value:
            return settings.FRONTEND_PORT
        elif container_type == ContainerType.BACKEND.value:
            return settings.BACKEND_PORT
        elif container_type == ContainerType.DATABASE.value:
            return settings.DATABASE_PORT
        elif container_type == ContainerType.MOBILE.value:
            return None
        
        return ports_to_map.pop(ContainerType.FRONTEND.value, 0)
    
    if not project_details.get("Manifest") or project_details.get("Scope"):
        print(generate_timestamp(),"-- MANIFEST", container_details_hash_map)
        containers = []
        for container_id, container_details in container_details_hash_map.items():
            container_details["container_id"] = container_id
            container = {
            "container_name": container_details.get("Title"),
            "description": container_details.get("description", ""),
            "interfaces": container_details.get("interfaces", ""),
            "container_type": get_container_type(container_details),
            "dependent_containers": container_details.get("dependent_containers", []),
            "workspace": container_details.get("repository", {}).get("repositoryName", ""),
            "container_root": container_details.get("container_root", ""),
            "port": get_container_port(container_details),
            "framework": container_details.get("framework", ""),
            "type": container_details.get("type", ""),
            "buildCommand": container_details.get("buildCommand", ""),
            "startCommand": container_details.get("startCommand", ""),
            "installCommand": container_details.get("installCommand", ""),
            "lintCommand": container_details.get("lintCommand", ""),
            "working_dir": container_details.get("working_dir", ""),
            "container_details": container_details.get("container_details"),
            "lintConfig": container_details.get("lintConfig", ""),
            "routes": container_details.get("routes", []),
            "apiSpec": container_details.get("apiSpec", ""),
            "auth": container_details.get("auth"),
            "schema": container_details.get("schema", ""),
            "migrations": container_details.get("migrations", ""),
            "seed": container_details.get("seed", ""),
            "env": container_details.get("env", {}),
            "private": container_details.get("private", {})
            }
            if container['container_type'] == 'mobile':
                container.pop('port', None)
                
            containers.append(container)
        
        
        project_schema = {
            "overview": {
                "project_name": project_details.get("Title", ""),
                "description": project_details.get("Description", ""),
                "third_party_services": []
            },
            "containers": containers
        }
        
        print(generate_timestamp(),'Project Schema here', project_schema)
        project_schema = dict_to_project_schema(project_schema)
            
        manifest_string = convert_manifest_to_yaml_string(project_schema)
        project_details["Manifest"] = manifest_string
    # Update project_repositories and current_repositories based on results
    for result in container_results:
        container_id = result["container_id"]
        repository_data = result["repository_data"]
        is_new = result["is_new"]
        
        if is_new:
            project_repositories[container_id] = repository_data
        
        project_details["current_repositories"].append(repository_data)
            
    await db.update_node_by_id(
            project_id,
            {"repositories": json.dumps(project_repositories)}
        )

    if test_case:
        work_items = await db.get_work_items_for_testcase(project_id)
    else:
        work_items = await db.get_work_items_for_container(container_id=container_id)


    return project_details, work_items