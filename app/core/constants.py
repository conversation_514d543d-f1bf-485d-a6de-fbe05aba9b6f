# constants.py
from enum import Enum
import os

def get_figma_base_path():
    if os.environ.get("LOCAL_DEBUG"):
        return "/tmp/figma"
    else:
        return "/app/data/figma"


FIGMA_BASE_PATH = get_figma_base_path()

### MONGO COLLECTIONS
CODE_GEN_ATTACHMENT_COLLECTION = "code_gen_attachments"
TASKS_COLLECTION_NAME = "code_gen_tasks"
TASK_FRAMEWORK_COLLECTION_NAME = "tf_tasks"
SCM_CONFIGURATION_COLLECTION= "scm_configurations"
DEPLOYMENT_COLLECTION_NAME = "app_deployments"
KUBERNETES_MONITOR_COLLECTION_NAME = "kubernetes_monitor"
PODS_COLLECTION = "pods_available"
USER_PODS_COLLECTION = "pods-user"

REPOSITORIES_COLLECTION = "project_repositories"

class NodeType(str, Enum):
    DEPLOYMENT = "Deployment"
    PROJECT = "Project"
    PRODUCT = "Product"
    REQUIREMENTROOT = "RequirementRoot"
    REQUIREMENT = "Requirement"
    WORKITEMROOT = "WorkItem"
    WORKITEM = "WorkItem"
    ARCHITECTURE = "Architecture"
    ARCHITECTUREROOT = "ArchitectureRoot"
    DESIGN = "Design"
    INTERFACE = "Interface"
    EPIC = "Epic"
    USERSTORY = "UserStory"
    TASK = "Task"
    DISCUSSION = "Discussion"
    USER = "User"
    ARCHITECTURALREQUIREMENT = "ArchitecturalRequirement"
    COMPONENT = "Architecture"
    SUB_COMPONENT = "Sub_Component"
    FUNCTIONALREQUIREMENT = "FunctionalRequirement"
    ARCHITECTURALREQUIREMENTNODE = "ArchitecturalRequirementNode",
    STATEDIAGRAM = "StateDiagram",
    ROBUSTNESSTEST = "RobustnessTest",
    STATELOGIC = "StateLogic",
    DIAGRAM = "Diagram",
    CLASSDIAGRAM = "ClassDiagram",
    DESIGNELEMENT ="DesignElement",
    ALGORITHM = "Algorithm",
    PERFORMANCETEST = "PerformanceTest",
    UNITTEST = "UnitTest",
    INTEGRATIONTEST = "IntegrationTest",
    SEQUENCE = "Sequence",
    TEST = "Test"
    CONTAINER = "Container"
    SYSTEMCONTEXT= "SystemContext"
    SUB_SECTION="Sub_Section"
    DOCUMENTATION="Documentation"
    DOCUMENTATIONROOT="DocumentationRoot"
    FUNCTIONALTESTCASE = "FunctionalTestCase"
    NONFUNCTIONALTESTCASE = "NonFunctionalTestCase"
    DATABASE = "Database"
    
    
class NodeLabel(list, Enum):
    EPIC = ["Epic", "Requirement"]
    USERSTORY = ["UserStory", "Requirement"]
    TASK = ["Task", "Requirement"]
    
    
USER_SETTING_SAGEMAKER = {
    "RStudioServerProAppSettings": {
        "AccessStatus": "DISABLED"
    },
    "StudioWebPortalSettings": {
        "HiddenAppTypes": [
        "JupyterLab",
        "Canvas",
        "JupyterServer",
        "RStudioServerPro"
        ],
        "HiddenMlTools": [
        "Experiments",
        "DataWrangler",
        "FeatureStore",
        "EmrClusters",
        "AutoMl",
        "Training",
        "ModelEvaluation",
        "Pipelines",
        "Models",
        "JumpStart",
        "InferenceRecommender",
        "Endpoints",
        "Projects"
        ]
    }
}

CLONE_REPO_TEMPLATE = """
#!/bin/bash
set -eux

sudo aws configure set aws_access_key_id {aws_access_key_id}
sudo aws configure set aws_secret_access_key {aws_secret_access_key}
sudo aws configure set default.region {region_name}



sudo git config --global credential.helper '!aws codecommit credential-helper $@'
sudo git config --global credential.UseHttpPath true
sudo git config --global user.name {user_name}
sudo git config --global user.email {email}



git clone {clone_url}


echo 'Script execution completed.'
"""

CLONE_TEMPLATE_VARIABLES = [
    "aws_access_key_id",
    "aws_secret_access_key",
    "region_name",
    "user_name",
    "email",
    "clone_url"
]

user_data_script = '''#!/bin/bash
    set -e


    # Log all output to a file
    exec > >(tee /var/log/user-data.log | logger -t user-data -s 2>/dev/console) 2>&1

    # Example commands
    echo "Starting userData script"

    sudo -i -u ubuntu bash << EOF

    cd /home/<USER>
    date
    export HOME=/home/<USER>

    # Update and install git
    apt-get update -y
    apt-get install -y git

    git config --global user.name {user_name}
    git config --global user.email {email}

    echo "Starting clone"
    # Clone the repository
    git clone {repo_url}
    echo "clone done"
    EOF
    '''
class TaskStatus(str, Enum):
    PENDING = 'pending'
    PAUSED = 'paused'
    IN_PROGRESS = 'in_progress'
    COMPLETE = 'complete'
    PENDING_SUBTASKS = 'pending_subtasks'
    FAILED = 'failed'
    CANCELLED = 'cancelled'
    RUNNING = 'running'
    STOPPED = 'stopped'
    RETRY = 'retrying'
    DEPLOYING = 'deploying'
    BUILDING = 'building'
    

GIT_IGNORE_CONTENT = """
# Python ignorables
logs/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

pip-log.txt
pip-delete-this-directory.txt
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
venv/
.env
.env.*

# Ignore all directories containing pyvenv.cfg
**/pyvenv.cfg
**/*/pyvenv.cfg

# Node.js ignorables
node_modules/
npm-debug.log
yarn-error.log
.pnp/
.pnp.js
.env
.env.test
.cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.file_activity_report.json
*.db
core.*
.core
*.log
post_process_status.lock
"""
# Create a new professional CSS template
MODERN_PDF_CSS = """
    /* Page Setup */
    @page {
        size: A4;
        margin: 2cm 2cm;
        @top-right {
            content: string(document-title);
            font-family: 'Inter', sans-serif;
            font-size: 9pt;
            color: #4A5568;
            padding: 0.5cm 0;
        }
        @bottom-right {
            content: counter(page) " of " counter(pages);
            font-family: 'Inter', sans-serif;
            font-size: 9pt;
            color: #4A5568;
        }
    }

    /* Base Styles */
    body {
        font-family: 'Inter', sans-serif;
        font-size: 10.5pt;
        line-height: 1.4;
        color: #2D3748;
        margin: 0;
        padding: 0;
    }

    /* Title Page */
    .title-page {
        text-align: center;
        padding-top: 8cm;
        page-break-after: always;
    }

    .title-page h1 {
        font-size: 24pt;
        font-weight: 600;
        color: #1A365D;
        margin-bottom: 1cm;
        line-height: 1.2;
    }

    /* Table of Contents */
    .toc-nav {
        page-break-after: always;
    }

    .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc-item {
        display: flex;
        align-items: baseline;
        margin: 0.3cm 0;
        line-height: 1.2;
    }

    .toc-item a {
        flex: 1;
        text-decoration: none;
        color: #2D3748;
    }

    .toc-item a::after {
        content: leader('.') target-counter(attr(href), page);
        color: #4A5568;
    }

    /* Headings */
    h1, h2, h3, h4, h5, h6 {
        color: #1A365D;
        line-height: 1.2;
        margin: 1em 0 0.5em 0;
        page-break-after: avoid;
    }

    h1 {
        font-size: 16pt;
        font-weight: 600;
    }

    h2 {
        font-size: 14pt;
        font-weight: 600;
    }

    h3 {
        font-size: 12pt;
        font-weight: 600;
    }

    /* Content Spacing */
    p {
        margin: 0.5em 0;
        text-align: justify;
        orphans: 3;
        widows: 3;
    }

    /* Lists */
    ul, ol {
        margin: 0.5em 0;
        padding-left: 1.2em;
    }

    ul ul, ul ol, ol ul, ol ol {
        margin: 0.2em 0;
        padding-left: 1.2em;
    }

    li {
        margin: 0.2em 0;
        line-height: 1.4;
    }

    /* Chapter Sections */
    .chapter {
        page-break-before: always;
    }

    .chapter:first-of-type {
        page-break-before: avoid;
    }

    .chapter-title {
        font-size: 16pt;
        font-weight: 600;
        color: #1A365D;
        margin: 1em 0 0.7em 0;
        padding-bottom: 0.2cm;
        border-bottom: 1px solid #E2E8F0;
        page-break-after: avoid;
    }

    /* Code Blocks */
    pre {
        background: #F7FAFC;
        border: 1px solid #E2E8F0;
        border-radius: 4px;
        padding: 0.5em;
        margin: 0.5em 0;
        font-family: 'Courier New', monospace;
        font-size: 9pt;
        line-height: 1.3;
        white-space: pre-wrap;
        page-break-inside: avoid;
    }

    code {
        font-family: 'Courier New', monospace;
        font-size: 9pt;
        background: #F7FAFC;
        padding: 0.1em 0.3em;
        border-radius: 3px;
    }

    /* Page Breaks */
    h1, h2 {
        page-break-after: avoid;
    }

    img, table, figure {
        page-break-inside: avoid;
    }

    /* Print Specific */
    @media print {
        * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
    }

    /* Tables */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 0.5em 0;
        font-size: 10pt;
    }

    th {
        background: #F7FAFC;
        border-bottom: 2px solid #E2E8F0;
        padding: 0.3em;
        text-align: left;
    }

    td {
        border-bottom: 1px solid #E2E8F0;
        padding: 0.3em;
    }
"""
