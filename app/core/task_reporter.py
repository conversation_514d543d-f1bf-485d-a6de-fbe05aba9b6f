import asyncio
import json
import time
from datetime import datetime
from queue import Queue, Empty
from collections import OrderedDict
from enum import Enum
from app.connection.establish_db_connection import get_mongo_db
from app.core.constants import TASKS_COLLECTION_NAME
from app.core.websocket.client import WebSocketClient
from app.models.code_generation_model import Message
from app.utils.code_generation_utils import initialize_filewatch_dir
from code_generation_core_agent.agents.task_execution_agent import TaskExecutionAgent, TaskExecutionReporter
from code_generation_core_agent.agents.tools.AgentPreProcessor import FunctionCallDescription
from code_generation_core_agent.chat.cga_chat import  MessageStatus, MessageType, ChatMessage
from app.core.file_watch import FileWatch
from app.connection.tenant_middleware import get_tenant_id
import os 
from app.utils.datetime_utils import generate_timestamp
from app.utils.cost_utils import check_free_credit_limits_crossed_sync
from app.utils.file_utils.upload_utils import upload_and_process
import hashlib, random
from code_generation_core_agent.chat.cga_chat import ChatMessage
from app.utils.code_generation_utils import custom_asdict_factory
from dataclasses import asdict
import threading
from app.services.session_tracker import get_session_tracker
from app.core.chat_controller import ChatInterfaceThread
import functools

def run_in_daemon_thread(func):
    """Decorator to run function in a daemon thread"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread = threading.Thread(
            target=func,
            args=args,
            kwargs=kwargs,
            daemon=True,
            name=f"{func.__name__}_daemon_{threading.current_thread().ident}"
        )
        thread.start()
        return thread
    return wrapper

def thread_safe_operation(func):
    """Decorator for thread-safe database operations with error handling"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"Thread-safe operation error in {func.__name__}: {e}")
            import traceback
            traceback.print_exc()
            return None
    return wrapper

# Moved to GitController: get_sensitive_fields_from_settings

# Moved to GitController: check_diff_for_sensitive_data

# Moved to GitController: add_to_gitignore

# Moved to GitController: unstage_sensitive_files
    
def custom_asdict_factory(data):
    def convert(obj):
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    return {k: convert(v) for k, v in data}


def id_generator(token=None):
    if token is None:
        token = str(random.uniform(0, 1))
    if type(token) != str:
        token = str(token)
    token = token.encode()
    m = hashlib.md5()
    m.update(token)
    return m.digest().hex()

def extract_search_replace(text):
    """
    Extract search and replace patterns from text containing correctly formatted merge conflict markers.
    Handles special cases where file paths are embedded in the REPLACE tag line.
    
    Args:
        text (str): The text containing search and replace patterns
        
    Returns:
        list: A list of dictionaries, each containing 'file_path', 'search', and 'replace' fields
    """
    results = []
    
    # Define the exact tags we're looking for
    SEARCH_TAG = "<<<<<< SEARCH"
    SEPARATOR_TAG = "======="
    REPLACE_TAG = ">>>>>>> REPLACE"
    
    # Split the text into lines for processing
    lines = text.split('\n')
    
    # First find all file paths
    import re
    file_paths = []
    for line in lines:
        if "tic_tac_toe_app/" in line:
            path_matches = re.findall(r'(tic_tac_toe_app/[^\s,"]+)', line)
            file_paths.extend(path_matches)
    
    # Now process search/replace blocks
    current_block = 0
    i = 0
    while i < len(lines):
        # Look for the start of a search block
        if i < len(lines) and SEARCH_TAG in lines[i]:
            search_start = i + 1
            i += 1
            
            # Find the separator
            while i < len(lines) and SEPARATOR_TAG not in lines[i]:
                i += 1
                
            if i < len(lines) and SEPARATOR_TAG in lines[i]:
                search_end = i
                replace_start = i + 1
                i += 1
                
                # Find the end of replace
                while i < len(lines) and REPLACE_TAG not in lines[i]:
                    i += 1
                    
                # Only process if we found the exact REPLACE_TAG
                if i < len(lines) and REPLACE_TAG in lines[i]:
                    replace_end = i
                    
                    # Extract the content
                    search_content = '\n'.join(lines[search_start:search_end]).strip()
                    replace_content = '\n'.join(lines[replace_start:replace_end]).strip()
                    
                    # Get file path for this block
                    file_path = None
                    if current_block < len(file_paths):
                        file_path = file_paths[current_block]
                    
                    # Only add if we have valid file path and both search and replace content
                    if file_path and search_content and replace_content:
                        results.append({
                            'file_path': file_path,
                            'search': search_content,
                            'replace': replace_content
                        })
                    
                    current_block += 1
            
        i += 1
    
    return results

class TaskReporter(TaskExecutionReporter):
    def __init__(self, task_id, agent: TaskExecutionAgent, db = None, ws_client: WebSocketClient = None):
        self.task_id = task_id
        self.agent = agent
        self.ws_client = ws_client
        self.is_user_input_requested = False
        self._user_input_queue = Queue()
        self._ws_task = None
        self._notification_task = None
        self._last_user_input = None
        self.tasks_collection_name = TASKS_COLLECTION_NAME
        self.db = db
        self.filewatch:FileWatch = None
        self.controller = None
        self.agent_cost_from_last_callback = {}
        self.code_update_stream_id = None
        self.docs_folder = self.get_docs_folder(task_id)
        self.streaming_id_flag = None
        self.streaming_content = ''
        self.streamed_edits = []
        self.previous_app_state = None
        self.is_files_updated = False
        self.chat_controller :ChatInterfaceThread = None
        self._app_state_first_trigger = True  # Track if app state callback is triggered for first time
        
        # Thread safety
        self._lock = threading.RLock()
        self._active_threads = set()

    def initialize(self):
        try:
            connected = self.ws_client.connect()
            self.filewatch = initialize_filewatch_dir()
            return connected
        except Exception as e:
            print(f"Failed to initialize reporter WebSocket: {e}")
            return False
        
    def set_chat_controller(self, chat_controller:ChatInterfaceThread):
        with self._lock:
            self.chat_controller = chat_controller

    @thread_safe_operation
    def update_message(self, message_id, updated_fields):
        # Get the task document from collection
        task_doc = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
        
        # Initialize result variable
        result = None
        
        if task_doc:
            # Get messages array from document
            messages = task_doc.get("messages", [])
            
            # Find and update the specific message
            message_updated = False
            for i, message in enumerate(messages):
                if message.get("id") == message_id:
                    # Update each field in the message
                    for key, value in updated_fields.items():
                        messages[i][key] = value
                    message_updated = True
                    break
            
            # If message wasn't found, create it (upsert behavior)
            if not message_updated and message_id:
                new_message = {"id": message_id}
                for key, value in updated_fields.items():
                    new_message[key] = value
                messages.append(new_message)
            
            # Update the document with modified messages array
            result = self.db[TASKS_COLLECTION_NAME].update_one(
                {"_id": self.task_id},
                {"$set": {"messages": messages}}
            )
        else:
            # If document doesn't exist, create it with the new message
            new_message = {"id": message_id}
            for key, value in updated_fields.items():
                new_message[key] = value
                
            result = self.db[TASKS_COLLECTION_NAME].insert_one({
                "_id": self.task_id,
                "messages": [new_message]
            })

        self.db[TASKS_COLLECTION_NAME].update_one(
            {"_id": self.task_id},
            {"$inc": {"updated_count": 1}}
        )
        print("RESULT", result)
        return result
    
    @thread_safe_operation
    def get_message_by_id(self, message_id):
        try:
            message = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id}, {"messages": 1})
            for msg in message.get("messages", []):
                if msg.get("id") == message_id:
                    return msg
            return None
        except Exception as e:
            print(f"Error getting message by id: {e}")
            return None

    @run_in_daemon_thread
    def _commit_changes_thread(self):
        """Thread-safe commit changes implementation that delegates to GitController"""
        try:
            print(f"Starting commit changes in thread {threading.current_thread().name}")
            
            # Check if we have a git controller available
            if not self.controller or not hasattr(self.controller, 'git_controller') or not self.controller.git_controller:
                print("No git controller available for commit changes")
                return False
                
            git_controller = self.controller.git_controller
            
            # Call the GitController's commit_changes method
            result = git_controller.commit_changes(chat_controller=self.chat_controller)
            
            if result:
                print(f"✅ Commit changes completed: {len(result.check_points)} checkpoints created")
                return True
            else:
                print("❌ Commit changes failed")
                return False
                
        except Exception as e:
            print(f"❌ Error in commit changes thread: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            print(f"🏁 Commit changes thread {threading.current_thread().name} completed")

    def commit_changes(self):
        """Public method to initiate commit changes in background thread"""
        return self._commit_changes_thread()

    @thread_safe_operation  
    def update_activity(self):
        try:
            if self.controller:
                self.controller._update_activity()
        except Exception as e:
            print(f"Error updating activity: {e}")
            pass
            
        
    def get_docs_folder(self, task_id):
        if(task_id.startswith("deep-query")):
            return "deep_query_docs"
        elif(task_id.startswith("cm")):
            return "code_maintenance_docs"
        else:
            return "code_generation_docs"
        
    def set_controller(self, controller):
        with self._lock:
            self.controller = controller
        
    @run_in_daemon_thread
    def _auto_commit_thread(self, context=None):
        """
        Thread-safe auto-commit implementation
        """
        try:
            print(f"Starting auto-commit in thread {threading.current_thread().name}")
            print("AUTO COMMIT STARTED-------------------------------")
            return True # TODO:ROLLBACK
            # Validate controller and git_controller availability
            if not self.controller or not hasattr(self.controller, 'git_controller'):
                print("No git controller available for auto-commit")
                return False
                
            git_controller = self.controller.git_controller
            all_repository_metadata = git_controller.repository_metadata
            
            if not all_repository_metadata or not isinstance(all_repository_metadata, list):
                print("No repository metadata available for auto-commit")
                return False
            
            success = False
            for repository_metadata in all_repository_metadata:
                # Get current base path for this repository
                current_path = repository_metadata.get("current_path")
                if not current_path:
                    continue
                    
                # Create git tool for this repository
                git_tool = EnhancedGitTools(
                    callback_functions=None,
                    base_path=current_path,
                    logger=None,
                    repository_metadata=repository_metadata,
                    tenant_id=get_tenant_id()
                )
                
                # Check if there are changes to commit
                try:
                    status = git_tool.git_status(repository_path=current_path)
                    if "nothing to commit" in status.lower() or "working tree clean" in status.lower():
                        continue  # No changes to commit
                    
                    # Configure git identity if needed
                    try:
                        # Check if git user is configured
                        config_name = subprocess.run(
                            ["git", "config", "user.name"],
                            cwd=current_path,
                            capture_output=True,
                            text=True
                        )
                        config_email = subprocess.run(
                            ["git", "config", "user.email"],
                            cwd=current_path,
                            capture_output=True,
                            text=True
                        )
                        
                        # Set git identity if not configured
                        if not config_name.stdout.strip() or not config_email.stdout.strip():
                            print("Setting git identity for commits")
                            subprocess.run(
                                ["git", "config", "user.name", "Kavia Bot"],
                                cwd=current_path, 
                                check=True
                            )
                            subprocess.run(
                                ["git", "config", "user.email", "<EMAIL>"],
                                cwd=current_path, 
                                check=True
                            )
                    except Exception as e:
                        print(f"Error configuring git identity: {e}")
                        # Continue anyway, as the commit might still work
                    
                    # Create timestamp-based commit message
                    timestamp = generate_timestamp()
                    commit_message = f"Auto-commit: Changes as of {timestamp}"
                    
                    # Add step info to message if available
                    step_info = context.get("step_info", {}) if context else {}
                    if step_info and isinstance(step_info, dict):
                        step_title = step_info.get("title", "")
                        if step_title:
                            commit_message = f"Auto-commit: {step_title} - {timestamp}"
                    
                    # Add all changes - with retry
                    for attempt in range(3):
                        try:
                            add_result = git_tool.git_add_all(repository_path=current_path)
                            print(f"Add result: {add_result}")
                            break
                        except Exception as e:
                            print(f"Error adding files (attempt {attempt+1}): {e}")
                            if attempt == 2:  # Last attempt failed
                                continue  # Skip to next repository
                            time.sleep(1)  # Brief pause before retry
                    
                    # Commit changes - with retry
                    for attempt in range(3):
                        try:
                            commit_result = git_tool.git_commit(
                                commit_message, 
                                repository_path=current_path
                            )
                            print(f"Commit result: {commit_result}")
                            success = True
                            break
                        except Exception as e:
                            print(f"Error committing changes (attempt {attempt+1}): {e}")
                            if attempt == 2:  # Last attempt failed
                                break  # Continue to push if needed
                            time.sleep(1)  # Brief pause before retry
                    
                    # Push changes if auto_push is enabled
                    if git_controller.auto_push and success:
                        for attempt in range(3):
                            try:
                                push_result = git_tool.git_push(repository_path=current_path)
                                print(f"Push result: {push_result}")
                                break
                            except Exception as e:
                                print(f"Error pushing changes (attempt {attempt+1}): {e}")
                                if attempt == 2:  # Last attempt failed
                                    break
                                time.sleep(1)  # Brief pause before retry
                except Exception as e:
                    print(f"Error processing repository {repository_metadata.get('repositoryName', 'unknown')}: {e}")
            
            return success
        except Exception as e:
            print(f"Error in auto-commit thread: {e}")
            return False
        finally:
            print(f"Auto-commit thread {threading.current_thread().name} completed")

    def auto_commit(self, context=None):
        """Public method to trigger auto-commit in background thread"""
        return self._auto_commit_thread(context)
    
    def clear_states(self):
        with self._lock:
            self._last_user_input = None
            self.is_user_input_requested = False
            self._user_input_queue = Queue()

    async def get_user_input(self, prompt):
        """Enhanced user input handling"""
        try:
            self.send_agent_message(prompt)
            self.is_user_input_requested = True

            # Update MongoDB status
            task = self.db[self.tasks_collection_name].find_one({"_id": self.task_id})
            if task:
                project_id = task.get("project_id")
                design_id = task.get("architecture_id")
                task_id = task.get("_id")
                receiver_id = task.get("user_id")
                self.db[self.tasks_collection_name].update_one(
                    {"_id": self.task_id}, 
                    {"$set": {"waiting_for_user": True}}
                )
            try:
                # Check for existing input first
                if self._last_user_input:
                    user_input = self._last_user_input
                    self._last_user_input = None
                    return user_input

                # Wait for new input
                start_time = time.time()
                while time.time() - start_time < 1800:  # 30 minutes timeout
                    try:
                        user_input = self._user_input_queue.get(timeout=1.0)
                        if user_input:
                            # Update MongoDB with received input
                            self.db[self.tasks_collection_name].update_one(
                                {"_id": self.task_id},
                                {
                                    "$set": {"waiting_for_user": False},
                                    "$push": {"user_inputs": {
                                        "timestamp": generate_timestamp(),
                                        "input": user_input
                                    }}
                                }
                            )
                            return user_input
                    except Empty:
                        continue

                raise TimeoutError("User input timeout reached")

            finally:
                self.is_user_input_requested = False
                if self._notification_task:
                    self._notification_task.cancel()
                    self._notification_task = None

        except Exception as e:
            print(f"Error getting user input: {e}")
            self.is_user_input_requested = False
            raise

    def collect_step_information(self, request_context):
        try:
            context = json.loads(request_context) if request_context else {}
            
            if 'last_step' in context:
                last_step = context['last_step']
                if isinstance(last_step, str):
                    try:
                        last_step = json.loads(last_step)
                    except json.JSONDecodeError:
                        print(f"Error decoding last_step JSON: {last_step}")
                        last_step = {}
                
                if isinstance(last_step, dict):
                    step_title = last_step.get('action', 'Unknown Action')
                    agent = last_step.get('Agent', 'Unknown Agent')
                    action = last_step.get('action', 'Unknown Action')
                    details = json.dumps(last_step.get('request_details', {}), indent=4)
                    
                    new_step = {
                        'title': step_title,
                        'agent': agent,
                        'action': action,
                        'details': details,
                        'context': context
                    }
                    
                    return new_step
     
            return {}
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON in progress_callback: {e}")
            return {}

    @run_in_daemon_thread
    def _progress_callback_thread(self, status, total_tasks, latest_result, request_context):
        """Thread-safe progress callback implementation"""
        try:
            print(f"Starting progress callback in thread {threading.current_thread().name}")
            
            # WebSocket update
            self.update_activity()
            new_step = self.collect_step_information(request_context)
            self.ws_client.send_message("progress_update", {
                "status": status,
                "total_tasks": total_tasks,
                "latest_result": latest_result,
                "step": new_step if new_step else None
            })
            _status = status.lower()
            if "completed" in _status and self.is_files_updated:
                print("COMMIT CHANGES")
                self.commit_changes()
                self.is_files_updated = False

            print("PROGRESSCALLBACK",{
                "status": status,
                "total_tasks": total_tasks,
                "latest_result": latest_result,
                "step": new_step if new_step else None
            })
            print(self.controller, self.controller.git_controller)
            print("Progress update sent to WebSocket client")
            if self.controller:
                print("Controller is set")
                self.controller.set_is_ready(True)
                self.ws_client.send_message("ready_check", {"status": "ready"})
                self.ws_client.send_message("current_model", {"model":  self.agent.get_selected_model()})
                self.ws_client.send_message("available_models", {"models": self.agent.get_available_models()})
                containers = self.agent.preview_manager.containers
                if not containers:
                    self.ws_client.send_message("containers", {
                        "status": "error", 
                        "message": "No containers found",
                        "timestamp": generate_timestamp()
                    })
                    return
                
                print(f"Found {len(containers)} containers for task {self.task_id}")
                container_list = []
                for container in containers.values():
                    container_dict = container.to_dict()
                    container_dict['name'] =container.container_name
                    container_dict['container_type'] = container.container_type
                    container_dict['framework'] = container.framework
                    container_list.append(container_dict)
                
                print("Containers", container_list)
                self.ws_client.send_message("containers", {
                    "status": "success",
                    "containers": container_list,
                    "timestamp": generate_timestamp()
                })
                

        
                print("PHASE INITIALIZED, START TIME: ", generate_timestamp())
                
            if self.controller and self.controller.git_controller:
                try:
                    context = {
                        "status": status,
                        "latest_result": latest_result,
                        "step_info": new_step,
                        "request_context": request_context
                    }
                    # Use thread-based auto-commit instead of subprocess
                    # self.trigger_auto_commit(context)

                except Exception as e:
                    print(f"Error triggering commit: {e}")
                

            # MongoDB update
            update_dict = {}
            if total_tasks:
                update_dict["total_tasks"] = total_tasks
            if latest_result:
                update_dict["latest_result"] = latest_result
            if request_context:
                update_dict["request_context"] = request_context
            if status:
                update_dict["_status"] = status

            update_query = {"$set": update_dict}
            
            if new_step and status:
                update_query["$push"] = {"past_steps": new_step, "past_statuses": status}
            elif new_step:
                update_query["$push"] = {"past_steps": new_step}
            elif status:
                update_query["$push"] = {"past_statuses": status}

            if update_dict:
                self.db[self.tasks_collection_name].update_one({"_id": self.task_id}, update_query)

        except Exception as e:
            print(f"Error in progress_callback thread: {e}")
        finally:
            print(f"Progress callback thread {threading.current_thread().name} completed")

    def progress_callback(self, status, total_tasks, latest_result, request_context):
        """Public method to trigger progress callback in background thread"""
        return self._progress_callback_thread(status, total_tasks, latest_result, request_context)

    def trigger_auto_commit(self, context=None):
        """
        Trigger auto-commit in a separate daemon thread to avoid blocking
        """
        return self.auto_commit(context)

    def universal_init_status_callback(self, container_name, status, message=None, error=None):
        """
        Callback function to handle universal initialization status updates.
        
        Args:
            container_name (str): Name of the container being initialized
            status (str): Current status of the initialization ('completed', 'success', 'error', etc.)
            message (str, optional): Additional status message
            error (str, optional): Error message if status indicates failure
        
        Returns:
            None
        """
        print(f"Universal init status callback: {container_name}, {status}, {message}, {error}")
        
        # Handle completed status
        if status == "completed":
            print("UNIVERSAL INIT COMPLETED, TRIGGERING COMMIT CHANGES")
            try:
                # Trigger commit changes in a separate thread
                result = self.commit_changes()
                print(f"Commit changes triggered: {result}")
            except Exception as e:
                print(f"Error committing changes: {e}")
                import traceback
                traceback.print_exc()
        
        # Handle success status
        elif status == "success":
            # Notify client about successful initialization
            if self.ws_client:
                self.ws_client.send_message("universal_init_success", {"container_name": container_name})
            else:
                print("Warning: WebSocket client not available to send success message")
        
        # Handle error status
        elif status == "error" and error:
            print(f"Universal init error for {container_name}: {error}")
            if self.ws_client:
                self.ws_client.send_message("universal_init_error", {
                    "container_name": container_name,
                    "error": error
                })
    
    @run_in_daemon_thread
    def _send_code_update_thread(self, stream_id, content, metadata=None):
        """Thread-safe code update implementation"""
        try:
            print(f"Starting code update in thread {threading.current_thread().name}")
            
            self.update_activity()
            """
            Handles sending code updates to the WebSocket client.
            
            Args:
                stream_id (str): Identifier for the stream
                content (str): Content to send
                metadata (dict, optional): Additional metadata about the content
            """
            print(f"STREAMING {stream_id} {content} {metadata}")
            try:
                if not metadata:
                    print("Warning: No metadata provided for code update")
                    return
                
                current_msg_id = None
                if self.chat_controller and self.chat_controller.current_message_id:
                    current_msg_id = self.chat_controller.current_message_id
                    
                operation = metadata.get('operation', None)
                if not operation:
                    print("Warning: No operation specified in metadata")
                    return
                    
                stream_type = metadata.get('type', 'start')
                
                file_path = metadata.get('file_path', None)
                if not file_path:
                    print("Warning: No file path specified in metadata")
                    return
                
                if self.task_id.startswith("cg"):
                    if file_path.startswith("/home/<USER>/workspace"):
                        file_path = '/'.join(file_path.split('/')[5:])
                    if file_path.startswith("home"):
                        file_path = '/'+ file_path
                        file_path = '/'.join(file_path.split('/')[5:])
                    
                file_name = file_path.split('/')[-1]
                
                # Handle WRITE operation
                if operation.lower() == 'write':
                    data = {
                        "message_id": id_generator(file_path),
                        "file_path": file_path,
                        "file_name": file_name,
                        "file_content": content,
                        "stream": True,
                        "is_end": stream_type == 'end',
                        "custom_chunk": False,
                        "operation": operation.lower()
                    }
                    self.ws_client.send_message("file_update", data)
                    
                    # Add file updates to database when the stream ends
                    if stream_type == 'end' and current_msg_id:
                        file_data = {
                            "message_id": id_generator(file_path),
                            "file_path": file_path,
                            "file_name": file_name,
                            "operation": operation.lower(),
                            "content": content,
                            "timestamp": generate_timestamp(),
                            "sender": "AI",
                            "msg_type": "file_update"
                        }
                        print("PUSHINGFILEDATA", file_data, current_msg_id)
                        message = self.get_message_by_id(current_msg_id)
                        print("MESSAGE", message)
                        file_updates = message.get("file_updates", [])
                        file_updates.append(file_data)
                        print("FILE UPDATES", file_updates)
                        result = self.update_message(current_msg_id, {"file_updates": file_updates})
                        print("RESULT", result)
                    
                # Handle EDIT operation
                elif operation.lower() == 'edit':
                    message_id = id_generator(file_path)
                    # Check if this is a new file being edited
                    if message_id != self.streaming_id_flag:
                        # Reset tracking for new file
                        self.streaming_content = content
                        self.streaming_id_flag = message_id
                        self.streamed_edits = []
                        
                        # Don't send an initial empty edit message
                        # Only extract and send if we have content with search/replace markers
                        extracted = extract_search_replace(content)
                        if extracted:
                            for edit_item in extracted:
                                data = {
                                    "message_id": message_id,
                                    "file_path": file_path,
                                    "file_name": file_name,
                                    "operation": "edit",
                                    "search": edit_item["search"],
                                    "replace": edit_item["replace"]
                                }
                                self.ws_client.send_message("file_update", data)
                                self.streamed_edits.append(edit_item)
                            
                            # Add file edits to database
                            if stream_type == 'end' and current_msg_id:
                                file_data = {
                                    "message_id": message_id,
                                    "file_path": file_path,
                                    "file_name": file_name,
                                    "operation": "edit",
                                    "edits": extracted,
                                    "timestamp": generate_timestamp(),
                                    "sender": "AI",
                                    "msg_type": "file_update"
                                }
                                print("PUSHINGFILEDATA", file_data, current_msg_id)
                                message = self.get_message_by_id(current_msg_id)
                                print("MESSAGE", message)
                                file_updates = message.get("file_updates", [])
                                file_updates.append(file_data)
                                print("FILE UPDATES", file_updates)
                                result = self.update_message(current_msg_id, {"file_updates": file_updates})
                                print("RESULT", result)
                        else:
                            data = {
                                    "message_id": message_id,
                                    "file_path": file_path,
                                    "file_name": file_name,
                                    "operation": "edit",
                                    "search": "",
                                    "replace": ""
                            }
                            self.ws_client.send_message("file_update", data)
                    else:
                        # Continuing edit to the same file
                        self.streaming_content += content
                        
                        # Extract search/replace patterns from the new content
                        extracted = extract_search_replace(content)
                        
                        # Only send new edits that haven't been processed before
                        if extracted:
                            # Track which edits we've already sent
                            sent_count = len(self.streamed_edits)
                            new_edits = extracted[sent_count:]
                            
                            for edit_item in new_edits:
                                # Validate the edit item has required fields
                                if not all(k in edit_item for k in ["search", "replace"]):
                                    print(f"Warning: Invalid edit item format: {edit_item}")
                                    continue
                                    
                                data = {
                                    "message_id": message_id,
                                    "file_path": file_path,
                                    "file_name": file_name,
                                    "operation": "edit",
                                    "search": edit_item["search"],
                                    "replace": edit_item["replace"]
                                }
                                self.ws_client.send_message("file_update", data)
                                self.streamed_edits.append(edit_item)
                            
                            # Add new file edits to database
                            if stream_type == 'end' and new_edits and current_msg_id:
                                file_data = {
                                    "message_id": message_id,
                                    "file_path": file_path,
                                    "file_name": file_name,
                                    "operation": "edit",
                                    "edits": new_edits,
                                    "timestamp": generate_timestamp(),
                                    "sender": "AI",
                                    "msg_type": "file_update"
                                }
                                print("PUSHINGFILEDATA", file_data, current_msg_id)
                                message = self.get_message_by_id(current_msg_id)
                                print("MESSAGE", message)
                                file_updates = message.get("file_updates", [])
                                file_updates.append(file_data)
                                print("FILE UPDATES", file_updates)
                                result = self.update_message(current_msg_id, {"file_updates": file_updates})
                                print("RESULT", result)
                        
                        # Make sure we store edits even when there are no new edits extracted
                        # but we're at the end of the stream
                        elif stream_type == 'end' and current_msg_id and self.streamed_edits:
                            file_data = {
                                "message_id": message_id,
                                "file_path": file_path,
                                "file_name": file_name,
                                "operation": "edit",
                                "edits": self.streamed_edits,
                                "timestamp": generate_timestamp(),
                                "sender": "AI",
                                "msg_type": "file_update"
                            }
                            print("PUSHINGFILEDATA (end of stream)", file_data, current_msg_id)
                            message = self.get_message_by_id(current_msg_id)
                            print("MESSAGE", message)
                            file_updates = message.get("file_updates", [])
                            file_updates.append(file_data)
                            print("FILE UPDATES", file_updates)
                            result = self.update_message(current_msg_id, {"file_updates": file_updates})
                            print("RESULT", result)
                else:
                    print(f"Warning: Unsupported operation type: {operation}")
            except Exception as e:
                print(f"Error in send_code_update: {e}")
                # Log more detailed error information
                import traceback
                traceback.print_exc()
        except Exception as e:
            print(f"Error in code update thread: {e}")
        finally:
            print(f"Code update thread {threading.current_thread().name} completed")

    def send_code_update(self, stream_id, content, metadata=None):
        """Public method to trigger code update in background thread"""
        return self._send_code_update_thread(stream_id, content, metadata)
    
    def send_code_panel_update(self, stream_id, content, metadata=None):
        print("Sending code panel update***************", stream_id,content, metadata)
        self.is_files_updated = True
        self.send_code_update(stream_id, content, metadata)
        
        data = {
            "stream_id": stream_id,
            "content": content
        }
    
    # Check if metadata exists and if it has document_type
        if metadata and 'type' in metadata and metadata['type'] == 'document_update':
            # Send document update message type
            self.ws_client.send_message("document_update", {
                "content": content,
                "file_path": metadata.get('file_path', ''),
                "operation": metadata.get('operation', '')
            })

        pass

    @run_in_daemon_thread
    def _terminal_output_callback_thread(self, output):
        """Thread-safe terminal output callback"""
        try:
            print(f"Starting terminal output in thread {threading.current_thread().name}")
            
            self.update_activity()
            id = self.task_id
            log_file = os.path.join("/home/<USER>/workspace", f".{id}_terminal_log.log")
            # Append output to log file with timestamp
            timestamp = datetime.now().strftime("%H:%M:%S")
            with open(log_file, "a", encoding="utf-8") as f:
                # Simply add a space at the beginning of each line using string replacement
                # This is more efficient than splitting and rejoining
                formatted_output = output.replace('\n', '\n ')
                formatted_output = formatted_output.replace(f"/home/<USER>/workspace/{id}","") if id.startswith("cm") or id.startswith("deep-query") else formatted_output.replace(f"/home/<USER>/workspace","") 
                # Ensure it starts with a space
                if not formatted_output.startswith(" "):
                    formatted_output = " " + formatted_output
                f.write(formatted_output + "\n")
                
            self.ws_client.send_message("terminal_output", {"output": output})
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id}, 
                {"$push": {"terminal_output": output}}
            )
        except Exception as e:
            print(f"Error in terminal output callback thread: {e}")
        finally:
            print(f"Terminal output thread {threading.current_thread().name} completed")

    def terminal_output_callback(self, output):
        """Public method to trigger terminal output in background thread"""
        return self._terminal_output_callback_thread(output)

    @run_in_daemon_thread
    def _browser_output_callback_thread(self, image):
        """Thread-safe browser output callback"""
        try:
            print(f"Starting browser output in thread {threading.current_thread().name}")
            
            self.update_activity()
            browser_output = ChatMessage(
                id=id_generator(),
                content="View the image in the browser",
                msg_type=MessageType.LLM,
                timestamp=generate_timestamp(),
                status=MessageStatus.COMPLETED
            )
            browser_output_dict = asdict(browser_output, dict_factory=custom_asdict_factory)
            self.ws_client.send_message("agent_message", browser_output_dict)
            self.ws_client.send_message("browser_output", {"output": image})
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id}, 
                {"$push": {"browser_output": image}}
            )
        except Exception as e:
            print(f"Error in browser output callback thread: {e}")
        finally:
            print(f"Browser output thread {threading.current_thread().name} completed")

    def browser_output_callback(self, image):
        """Public method to trigger browser output in background thread"""
        return self._browser_output_callback_thread(image)
         
    def _cost_update_callback_thread(self, agent_costs, total_cost):
        """Thread-safe cost update callback implementation"""
        try:
            print(f"Starting cost update in thread {threading.current_thread().name}")
            
            """
            Centralized cost tracking method for different organization types
            
            Args:
                agent_costs (dict): Detailed costs for individual agents
                total_cost (float): Total cost of the operation
            """
            try:
                # Validate inputs
                print(f"Agent cost: {agent_costs}, Total cost: {total_cost}")

                # Validate task_id is available for cost tracking
                if not self.task_id:
                    error_msg = f"CRITICAL ERROR: Cost update callback has no task_id!"
                    print(error_msg)
                    raise ValueError(error_msg)

                try:
                    print(f"[DEBUG] Starting session tracking for task_id: {self.task_id}")
                    # Get session tracker
                    tracker = get_session_tracker()
                    
                    # Update session cost (will create session if doesn't exist)
                    cost_result = asyncio.run(
                        tracker.update_session_cost(
                            task_id=self.task_id,
                            agent_costs=agent_costs,
                            total_cost=total_cost
                        )
                    )
                    
                    print(f"[DEBUG] Cost update result: {cost_result}")
                    
                    # Update session metadata with the information we have
                    if cost_result["success"]:
                        metadata_result = asyncio.run(
                            tracker.update_session_metadata(
                                task_id=self.task_id
                            )
                        )
                        print(f"[DEBUG] Metadata update result: {metadata_result}")
                    
                    if cost_result["success"]:
                        print(f"[SUCCESS] Session cost tracking updated for task_id: {self.task_id}")
                        print(f"[SUCCESS] Costs REPLACED - Total: {total_cost}, Agents: {agent_costs}")
                    else:
                        print(f"[ERROR] Failed to update session cost: {cost_result.get('error', 'Unknown error')}")
            
                except Exception as session_error:
                    print(f"[ERROR] Session cost tracking error: {session_error}")
                    import traceback
                    traceback.print_exc()
                self.update_activity()
                # Get database and collection info
                # if self.controller and self.controller.git_controller:
                #     try:
                #         # Use thread-based auto-commit instead of subprocess
                #         self.trigger_auto_commit()
                #     except Exception as e:
                #         print(f"Error triggering commit: {e}")
                if not isinstance(agent_costs, dict) or not isinstance(total_cost, (int, float)):
                    raise ValueError("Invalid cost input types")
                
                # Extract database details
                database_name = self.db.name
                db_parts = database_name.split('_')
                
                if len(db_parts) < 2:
                    print("Invalid database name structure")
                    return
                
                organization_id = db_parts[-1]
                prefix = '_'.join(db_parts[:-1])
                root_db_name = f"{prefix}_kaviaroot"
                
                # Connect to root database
                root_db = get_mongo_db().client[root_db_name]
                current_date = datetime.now().strftime('%Y-%m-%d')
                
                # Retrieve task document
                task_doc = self.db[TASKS_COLLECTION_NAME].find_one({"_id": self.task_id})
                if not task_doc:
                    print("No task document found")
                    return
                
                # Extract user and project details
                project_id = task_doc.get('project_id')
                project_details = json.loads(task_doc.get('project_details', '{}'))
                user_id = task_doc.get("user_id") or project_details.get('created_by')
                
                if not user_id:
                    print("No user identifier found")
                    return
                
                # Task history tracking
                task_history_collection = root_db['task_history']
                existing_task = task_history_collection.find_one({
                    "task_id": self.task_id,
                    "user_id": user_id
                })
                
                is_new_task = existing_task is None
                
                # Record new task if not exists
                if is_new_task:
                    task_history_collection.insert_one({
                        "task_id": self.task_id,
                        "user_id": user_id,
                        "first_seen": datetime.now().isoformat(),
                        "organization_id": organization_id
                    })
                
                # Routing cost tracking based on organization type
                if organization_id == "b2c":
                    self._track_b2c_costs(
                        root_db, 
                        user_id, 
                        total_cost, 
                        agent_costs, 
                        current_date, 
                        self.task_id,
                        project_id,
                        is_new_task
                    )
                else:
                    self._track_b2b_costs(
                        root_db, 
                        organization_id, 
                        total_cost, 
                        agent_costs, 
                        project_id, 
                        user_id, 
                        current_date,
                        self.task_id,
                        is_new_task
                    )
                
                # Credit limit and update operations
                limit_crossed = check_free_credit_limits_crossed_sync(tenant_id=organization_id, current_user=user_id)
                
                self.ws_client.send_message("cost_update", {
                    "agents_cost": agent_costs,
                    "total_cost": total_cost,
                    "limit_crossed": limit_crossed
                })
                
                self.db[TASKS_COLLECTION_NAME].update_one(
                    {"_id": self.task_id}, 
                    {"$set": {"agents_cost": agent_costs, "total_cost": total_cost}}
                )
            except Exception as e:
                print(f"Cost update error: {e}")
        except Exception as e:
            print(f"Error in cost update thread: {e}")
        finally:
            print(f"Cost update thread {threading.current_thread().name} completed")

    def cost_update_callback(self, agent_costs, total_cost):
        """Public method to trigger cost update in background thread"""
        return self._cost_update_callback_thread(agent_costs, total_cost)
            
    def _track_b2c_costs(self, root_db, user_id, total_cost, agent_costs, current_date, task_id, project_id, is_new_task):
        """
        Track costs for B2C organization with intelligent project and task-based management
        """
        try:
            print("\n🔍 Starting B2C Cost Tracking")
            print(f"📋 User ID: {user_id}")
            print(f"💰 Total Cost: ${total_cost:.6f}")
            print(f"🆔 Task ID: {task_id}")
            print(f"🏗️ Project ID: {project_id}")
            print(f"🆕 Is New Task (Parameter): {is_new_task}")
            print(f"🤖 Agents Costs: {agent_costs}")

            # Collections
            active_subscriptions_collection = root_db['active_subscriptions']
            llm_costs_collection = root_db['llm_costs']
            
            # Get current active subscription
            sub_doc = active_subscriptions_collection.find_one(
                {"user_id": user_id},
                sort=[("created_at", -1)]
            )
            print(f"🔐 Existing Subscription Document: {bool(sub_doc)}")
            
            # Debug: Print existing subscription document
            if sub_doc:
                print("🔍 Existing Subscription Details:")
                print(f"   Current Cost: {sub_doc.get('current_cost', '')}")
                print(f"   Organization Cost: {sub_doc.get('organization_cost', '')}")
                print(f"   Last Cost Update: {sub_doc.get('last_cost_update', '')}")
            
            # Determine current price_id and plan
            current_price_id = sub_doc.get("price_id") if sub_doc else "price_1RWBNuCI2zbViAE2N6TkeNVB"
            print(f"💳 Current Price ID: {current_price_id}")
            
            # Retrieve or create LLM costs document
            llm_cost_doc = llm_costs_collection.find_one({"organization_id": "b2c"}) or {
                "organization_id": "b2c",
                "organization_name": "Default B2C",
                "organization_cost": "$0.000000",
                "users": []
            }
            
            # Debug: Print existing LLM cost document
            print("\n🔍 Existing LLM Cost Document Structure:")
            print(f"   Total Users: {len(llm_cost_doc.get('users', []))}")
            print(f"   Organization Cost: {llm_cost_doc.get('organization_cost', 'N/A')}")
            
            # Find or create user entry
            user_entry = next(
                (user for user in llm_cost_doc.get("users", []) if user.get("user_id") == user_id),
                None
            )
            
            if not user_entry:
                print(f"🆕 Creating new user entry for {user_id}")
                user_entry = {
                    "user_id": user_id,
                    "type": "llm_interaction",
                    "cost": "$0.000000",
                    "current_plan": current_price_id,
                    "plans_history": [],
                    "projects": [],
                    "_task_tracking": {}  # Internal tracking for task cost management
                }
                llm_cost_doc.setdefault("users", []).append(user_entry)
            else:
                print(f"👤 Existing user entry found for {user_id}")
                # Ensure _task_tracking exists for backward compatibility
                if "_task_tracking" not in user_entry:
                    user_entry["_task_tracking"] = {}
            
            # Find or create project entry
            project_entry = next(
                (proj for proj in user_entry.get("projects", []) if proj.get("project_id") == project_id),
                None
            )
            
            if not project_entry:
                print(f"🆕 Creating new project entry for Project ID: {project_id}")
                project_entry = {
                    "project_id": project_id,
                    "project_cost": "$0.000000",
                    "agents": []
                }
                user_entry.setdefault("projects", []).append(project_entry)
            else:
                print(f"🏗️ Existing project entry found for Project ID: {project_id}")
            
            # === COST CALCULATION BLOCK ===
            # Store original values for comparison
            original_user_cost = float(user_entry.get("cost", "0").strip('$'))
            original_org_cost = float(llm_cost_doc.get("organization_cost", "0").strip('$'))
            original_subscription_cost = float(sub_doc.get("current_cost", "0").strip('$')) if sub_doc else 0.0
            
            # Task tracking for cost management - FIX STRING ISSUE HERE
            task_tracking = user_entry.get("_task_tracking", {})
            
            # **FIX: Ensure task_tracking is always a dictionary**
            if not isinstance(task_tracking, dict):
                print(f"⚠️ WARNING: task_tracking is corrupted (type: {type(task_tracking)}), resetting to empty dict")
                task_tracking = {}
                user_entry["_task_tracking"] = task_tracking
            
            task_key = f"{project_id}_{task_id}"
            previous_task_info = task_tracking.get(task_key, {})
            
            # **FIX: Ensure previous_task_info is always a dictionary**
            if not isinstance(previous_task_info, dict):
                print(f"⚠️ WARNING: previous_task_info is corrupted (type: {type(previous_task_info)}), resetting to empty dict")
                previous_task_info = {}
            
            # Extract previous task cost safely
            previous_total_cost = 0.0
            if "total_cost" in previous_task_info:
                try:
                    previous_total_cost = float(previous_task_info["total_cost"].strip('$'))
                except (ValueError, AttributeError) as e:
                    print(f"⚠️ WARNING: Could not parse previous task cost: {previous_task_info.get('total_cost')}, error: {e}")
                    previous_total_cost = 0.0
            
            # *** CORRECTED LOGIC BASED ON YOUR REQUIREMENTS ***
            # Determine if this is truly a new task based on task_tracking
            task_exists_in_tracking = task_key in task_tracking and isinstance(task_tracking[task_key], dict)
            
            if not task_exists_in_tracking:
                # NEW TASK ID - APPEND COST
                operation_type = "APPENDED"
                new_user_cost = original_user_cost + total_cost
                reason = "New task ID - appending cost to existing user total"
                is_actually_new_task = True
            else:
                # EXISTING TASK ID - REPLACE COST
                operation_type = "REPLACED"
                new_user_cost = original_user_cost - previous_total_cost + total_cost
                reason = f"Continuing same task - replacing previous cost of ${previous_total_cost:.6f}"
                is_actually_new_task = False

            # === COMPREHENSIVE COST TRACKING SUMMARY ===
            print("\n" + "="*80)
            print("💰 COMPREHENSIVE COST TRACKING SUMMARY")
            print("="*80)
            print(f"📋 Task ID: {task_id}")
            print(f"🏗️ Project ID: {project_id}")
            print(f"👤 User ID: {user_id}")
            print(f"🆕 Is New Task (Parameter): {is_new_task}")
            print(f"🔍 Task Exists in Tracking: {task_exists_in_tracking}")
            print(f"✅ Actually New Task: {is_actually_new_task}")
            print("-" * 80)
            print("💸 COST BREAKDOWN:")
            print(f"   Current Task Cost: ${total_cost:.6f}")
            print(f"   Previous Task Cost: ${previous_total_cost:.6f}")
            print(f"   Operation: {operation_type}")
            print(f"   Reason: {reason}")
            print("-" * 80)
            print("📊 USER COST CHANGES:")
            print(f"   Original User Cost: ${original_user_cost:.6f}")
            print(f"   New User Cost: ${new_user_cost:.6f}")
            print(f"   User Cost Delta: ${new_user_cost - original_user_cost:+.6f}")
            
            if operation_type == "APPENDED":
                print(f"   📈 APPEND LOGIC: ${original_user_cost:.6f} + ${total_cost:.6f} = ${new_user_cost:.6f}")
            else:
                print(f"   🔄 REPLACE LOGIC: ${original_user_cost:.6f} - ${previous_total_cost:.6f} + ${total_cost:.6f} = ${new_user_cost:.6f}")
            
            print("-" * 80)
            
            # Update user cost
            user_entry["cost"] = f"${new_user_cost:.6f}"
            
            # Update task tracking - SAFE UPDATE
            task_tracking[task_key] = {
                "total_cost": f"${total_cost:.6f}",
                "agent_costs": dict(agent_costs) if isinstance(agent_costs, dict) else {}
            }
            user_entry["_task_tracking"] = task_tracking
            
            # Aggregate agent costs for the project
            project_agent_totals = {}
            
            # Go through all tasks for this project to aggregate agent costs
            for t_key, t_info in task_tracking.items():
                if t_key.startswith(f"{project_id}_") and isinstance(t_info, dict):
                    agent_costs_for_task = t_info.get("agent_costs", {})
                    if isinstance(agent_costs_for_task, dict):
                        for agent_name, agent_cost in agent_costs_for_task.items():
                            if agent_name not in project_agent_totals:
                                project_agent_totals[agent_name] = 0
                            try:
                                project_agent_totals[agent_name] += float(agent_cost) if agent_cost else 0
                            except (ValueError, TypeError):
                                print(f"⚠️ WARNING: Invalid agent cost for {agent_name}: {agent_cost}")
            
            # Convert aggregated costs to the required format
            agent_cost_details = []
            for agent_name, agent_total in project_agent_totals.items():
                agent_cost_details.append({
                    "agent_name": agent_name,
                    "total_cost": f"${agent_total:.6f}"
                })
            
            # Update project with aggregated agent costs
            project_entry["agents"] = agent_cost_details
            
            # Recalculate project total cost
            project_total_cost = sum(project_agent_totals.values())
            project_entry["project_cost"] = f"${project_total_cost:.6f}"
            
            # Recalculate user total cost
            user_total_cost = sum(
                float(proj.get("project_cost", "0").strip('$')) 
                for proj in user_entry.get("projects", [])
            )
            user_entry["cost"] = f"${user_total_cost:.6f}"
            
            # Recalculate organization cost
            total_org_cost = sum(
                float(u.get("cost", "0").strip('$')) 
                for u in llm_cost_doc.get("users", [])
            )
            llm_cost_doc["organization_cost"] = f"${total_org_cost:.6f}"
            
            # Update the LLM costs document
            llm_costs_collection.replace_one(
                {"organization_id": "b2c"},
                llm_cost_doc,
                upsert=True
            )
            
            # Update or create active subscription document
            subscription_updated = False
            if sub_doc:
                print("🔄 Updating Existing Subscription")
                # Update existing subscription with additional tracking
                update_data = {
                    "organization_cost": f"{total_org_cost:.6f}",
                    "current_cost": f"${user_total_cost:.6f}",
                    "last_cost_update": datetime.now().isoformat(),
                    "llm_cost_timestamp": current_date
                }
                
                active_subscriptions_collection.update_one(
                    {"_id": sub_doc["_id"]},
                    {"$set": update_data}
                )
                subscription_updated = True
                new_subscription_cost = user_total_cost
            else:
                print("🆕 Creating New Subscription Document")
                # Create new subscription document
                new_subscription = {
                    "user_id": user_id,
                    "organization_cost": f"{total_org_cost:.6f}",
                    "current_cost": f"${user_total_cost:.6f}",
                    "price_id": current_price_id,
                    "created_at": datetime.now().isoformat(),
                    "last_cost_update": datetime.now().isoformat(),
                    "llm_cost_timestamp": current_date
                }
                
                active_subscriptions_collection.insert_one(new_subscription)
                subscription_updated = True
                new_subscription_cost = user_total_cost
            
            # === FINAL VERIFICATION BLOCK ===
            print("🔍 FINAL COST VERIFICATION:")
            print(f"   LLM Costs - User Cost: ${user_total_cost:.6f}")
            print(f"   Subscription - User Cost: ${new_subscription_cost:.6f}")
            print(f"   Costs Match: {'✅ YES' if abs(user_total_cost - new_subscription_cost) < 0.000001 else '❌ NO'}")
            print("-" * 80)
            print("🏢 ORGANIZATION COSTS:")
            print(f"   Original Org Cost: ${original_org_cost:.6f}")
            print(f"   New Org Cost: ${total_org_cost:.6f}")
            print(f"   Org Cost Delta: ${total_org_cost - original_org_cost:+.6f}")
            print("-" * 80)
            print("📈 SUBSCRIPTION UPDATE:")
            print(f"   Subscription Updated: {'✅ YES' if subscription_updated else '❌ NO'}")
            if subscription_updated:
                print(f"   Original Subscription Cost: ${original_subscription_cost:.6f}")
                print(f"   New Subscription Cost: ${new_subscription_cost:.6f}")
                print(f"   Subscription Delta: ${new_subscription_cost - original_subscription_cost:+.6f}")
            print("="*80)
            
            print("✅ B2C Cost Tracking Completed Successfully")
            
            return user_total_cost
            
        except Exception as e:
            print(f"❌ B2C Cost Tracking Error: {e}")
            import traceback
            traceback.print_exc()
            return None
            
  
    def _track_b2b_costs(self, root_db, organization_id, total_cost, agent_costs, project_id, user_id, current_date, task_id, is_new_task):
        """
        Track costs for B2B organization with intelligent project and task-based management
        Updates ONLY llm_costs collection (NO active_subscriptions)
        """
        try:
            print("\n🔍 Starting B2B Cost Tracking")
            print(f"📋 User ID: {user_id}")
            print(f"🏢 Organization ID: {organization_id}")
            print(f"💰 Total Cost: ${total_cost:.6f}")
            print(f"🆔 Task ID: {task_id}")
            print(f"🏗️ Project ID: {project_id}")
            print(f"🆕 Is New Task (Parameter): {is_new_task}")
            print(f"🤖 Agents Costs: {agent_costs}")

            # Get or create LLM costs document for the organization
            llm_costs_collection = root_db['llm_costs']
            llm_cost_doc = llm_costs_collection.find_one({"organization_id": organization_id}) or {
                "organization_id": organization_id,
                "organization_name": f"Organization {organization_id}",
                "organization_cost": "$0.000000",
                "users": []
            }
            
            print(f"\n🔍 Existing LLM Cost Document Structure:")
            print(f"   Total Users: {len(llm_cost_doc.get('users', []))}")
            print(f"   Organization Cost: {llm_cost_doc.get('organization_cost', 'N/A')}")
            
            # Find or create user entry
            user_entry = next(
                (user for user in llm_cost_doc.get("users", []) if user.get("user_id") == user_id),
                None
            )
            
            if not user_entry:
                print(f"🆕 Creating new user entry for {user_id}")
                user_entry = {
                    "user_id": user_id,
                    "user_cost": "$0.000000",
                    "projects": [],
                    "_task_tracking": {}  # Task tracking for B2B
                }
                llm_cost_doc.setdefault("users", []).append(user_entry)
            else:
                print(f"👤 Existing user entry found for {user_id}")
                # Ensure _task_tracking exists
                if "_task_tracking" not in user_entry:
                    user_entry["_task_tracking"] = {}
            
            # Find or create project entry
            project_entry = next(
                (proj for proj in user_entry.get("projects", []) if proj.get("project_id") == project_id),
                None
            )
            
            if not project_entry:
                print(f"🆕 Creating new project entry for Project ID: {project_id}")
                project_entry = {
                    "project_id": project_id,
                    "project_cost": "$0.000000",
                    "agents": []
                }
                user_entry.setdefault("projects", []).append(project_entry)
            else:
                print(f"🏗️ Existing project entry found for Project ID: {project_id}")
            
            # === COST CALCULATION LOGIC (SAME AS B2C) ===
            original_user_cost = float(user_entry.get("user_cost", "0").strip('$'))
            original_org_cost = float(llm_cost_doc.get("organization_cost", "0").strip('$'))
            
            # Task tracking with string error protection
            task_tracking = user_entry.get("_task_tracking", {})
            
            if not isinstance(task_tracking, dict):
                print(f"⚠️ WARNING: task_tracking corrupted (type: {type(task_tracking)}), resetting")
                task_tracking = {}
                user_entry["_task_tracking"] = task_tracking
            
            task_key = f"{project_id}_{task_id}"
            previous_task_info = task_tracking.get(task_key, {})
            
            if not isinstance(previous_task_info, dict):
                print(f"⚠️ WARNING: previous_task_info corrupted (type: {type(previous_task_info)}), resetting")
                previous_task_info = {}
            
            # Extract previous task cost safely
            previous_total_cost = 0.0
            if "total_cost" in previous_task_info:
                try:
                    previous_total_cost = float(previous_task_info["total_cost"].strip('$'))
                except (ValueError, AttributeError) as e:
                    print(f"⚠️ WARNING: Could not parse previous task cost: {previous_task_info.get('total_cost')}, error: {e}")
                    previous_total_cost = 0.0
            
            # Determine operation type based on task tracking
            task_exists_in_tracking = task_key in task_tracking and isinstance(task_tracking[task_key], dict)
            
            if not task_exists_in_tracking:
                operation_type = "APPENDED"
                new_user_cost = original_user_cost + total_cost
                reason = "New task ID - appending cost to existing user total"
                is_actually_new_task = True
            else:
                operation_type = "REPLACED"
                new_user_cost = original_user_cost - previous_total_cost + total_cost
                reason = f"Continuing same task - replacing previous cost of ${previous_total_cost:.6f}"
                is_actually_new_task = False

            # === COMPREHENSIVE B2B COST TRACKING SUMMARY ===
            print("\n" + "="*80)
            print("💰 COMPREHENSIVE B2B COST TRACKING SUMMARY")
            print("="*80)
            print(f"📋 Task ID: {task_id}")
            print(f"🏗️ Project ID: {project_id}")
            print(f"👤 User ID: {user_id}")
            print(f"🏢 Organization ID: {organization_id}")
            print(f"🆕 Is New Task (Parameter): {is_new_task}")
            print(f"🔍 Task Exists in Tracking: {task_exists_in_tracking}")
            print(f"✅ Actually New Task: {is_actually_new_task}")
            print("-" * 80)
            print("💸 COST BREAKDOWN:")
            print(f"   Current Task Cost: ${total_cost:.6f}")
            print(f"   Previous Task Cost: ${previous_total_cost:.6f}")
            print(f"   Operation: {operation_type}")
            print(f"   Reason: {reason}")
            print("-" * 80)
            print("📊 USER COST CHANGES:")
            print(f"   Original User Cost: ${original_user_cost:.6f}")
            print(f"   New User Cost: ${new_user_cost:.6f}")
            print(f"   User Cost Delta: ${new_user_cost - original_user_cost:+.6f}")
            
            if operation_type == "APPENDED":
                print(f"   📈 APPEND LOGIC: ${original_user_cost:.6f} + ${total_cost:.6f} = ${new_user_cost:.6f}")
            else:
                print(f"   🔄 REPLACE LOGIC: ${original_user_cost:.6f} - ${previous_total_cost:.6f} + ${total_cost:.6f} = ${new_user_cost:.6f}")
            print("-" * 80)
            
            # Update user cost
            user_entry["user_cost"] = f"${new_user_cost:.6f}"
            
            # Update task tracking
            task_tracking[task_key] = {
                "total_cost": f"${total_cost:.6f}",
                "agent_costs": dict(agent_costs) if isinstance(agent_costs, dict) else {}
            }
            user_entry["_task_tracking"] = task_tracking
            
            # Aggregate agent costs for the project (task-aware)
            project_agent_totals = {}
            
            # Go through all tasks for this project to aggregate agent costs
            for t_key, t_info in task_tracking.items():
                if t_key.startswith(f"{project_id}_") and isinstance(t_info, dict):
                    agent_costs_for_task = t_info.get("agent_costs", {})
                    if isinstance(agent_costs_for_task, dict):
                        for agent_name, agent_cost in agent_costs_for_task.items():
                            if agent_name not in project_agent_totals:
                                project_agent_totals[agent_name] = 0
                            try:
                                project_agent_totals[agent_name] += float(agent_cost) if agent_cost else 0
                            except (ValueError, TypeError):
                                print(f"⚠️ WARNING: Invalid agent cost for {agent_name}: {agent_cost}")
            
            # Update project with aggregated agent costs
            agent_cost_details = []
            for agent_name, agent_total in project_agent_totals.items():
                agent_cost_details.append({
                    "agent_name": agent_name,
                    "total_cost": f"${agent_total:.6f}",
                    "costs_by_date": {current_date: f"${agent_total:.6f}"},
                    "tokens_by_date": {}
                })
            
            project_entry["agents"] = agent_cost_details
            
            # Recalculate project total cost
            project_total_cost = sum(project_agent_totals.values())
            project_entry["project_cost"] = f"${project_total_cost:.6f}"
            
            # Recalculate user total cost
            user_total_cost = sum(
                float(proj.get("project_cost", "0").strip('$')) 
                for proj in user_entry.get("projects", [])
            )
            user_entry["user_cost"] = f"${user_total_cost:.6f}"
            
            # Recalculate organization cost
            total_org_cost = sum(
                float(u.get("user_cost", "0").strip('$')) 
                for u in llm_cost_doc.get("users", [])
            )
            llm_cost_doc["organization_cost"] = f"${total_org_cost:.6f}"
            
            # *** ONLY UPDATE LLM_COSTS (NO ACTIVE_SUBSCRIPTIONS FOR B2B) ***
            llm_costs_collection.replace_one(
                {"organization_id": organization_id},
                llm_cost_doc,
                upsert=True
            )
            print("💾 LLM Costs Document Updated Successfully")
            
            # === FINAL B2B VERIFICATION ===
            print("🔍 FINAL B2B COST VERIFICATION:")
            print(f"   LLM Costs - User Cost: ${user_total_cost:.6f}")
            print(f"   LLM Costs - Organization Cost: ${total_org_cost:.6f}")
            print("-" * 80)
            print("🏢 ORGANIZATION COSTS:")
            print(f"   Original Org Cost: ${original_org_cost:.6f}")
            print(f"   New Org Cost: ${total_org_cost:.6f}")
            print(f"   Org Cost Delta: ${total_org_cost - original_org_cost:+.6f}")
            print("-" * 80)
            print("📊 B2B COST UPDATE STATUS:")
            print(f"   LLM Costs Updated: ✅ YES")
            print(f"   Active Subscriptions Updated: ➖ N/A (B2B doesn't use subscriptions)")
            print("="*80)
            
            print("✅ B2B Cost Tracking Completed Successfully")
            
            return user_total_cost
            
        except Exception as e:
            print(f"❌ B2B Cost Tracking Error: {e}")
            import traceback
            traceback.print_exc()
            return None
        
    @run_in_daemon_thread
    def _app_state_callback_thread(self, url, state=None, container=None):
        """Thread-safe app state callback"""
        try:
            print(f"Starting app state callback in thread {threading.current_thread().name}")
            
            # Check if this is the first time app state callback is triggered
            if self._app_state_first_trigger:
                with self._lock:
                    if self._app_state_first_trigger:  # Double-check with lock
                        self._app_state_first_trigger = False
                        print("🎯 First time app state callback triggered - initiating commit changes")
                        try:
                            print("✅ First-time commit changes initiated successfully")
                        except Exception as commit_error:
                            print(f"❌ Error during first-time commit changes: {commit_error}")
            
            self.update_activity()
            print("Preview URL and state", url, state)
            try:
                # Determine preview status based on state and URL
                preview_status = "running" if url else "building"
                
                # Send unified preview_response message
                preview_data = {
                    "status": preview_status,
                    "url": url,
                    "timestamp": generate_timestamp(),
                    "metadata": {
                        "state": state,
                        "source": "app_state_callback"
                    }
                }
                
                self.ws_client.send_message("preview_response", preview_data)
                containers = self.agent.preview_manager.containers
                if not containers:
                    self.ws_client.send_message("containers", {
                        "status": "error", 
                        "message": "No containers found",
                        "timestamp": generate_timestamp()
                    })
                    return
                
                print(f"Found {len(containers)} containers for task {self.task_id}")
                container_list = []
                for container in containers.values():
                    container_dict = container.to_dict()
                    container_dict['name'] = container.container_name
                    container_dict['container_type'] = container.container_type
                    container_dict['framework'] = container.framework
                    container_list.append(container_dict)
                
                print("Containers", container_list)
                self.ws_client.send_message("containers", {
                    "status": "success",
                    "containers": container_list,
                    "timestamp": generate_timestamp()
                })
                
                # Also send the old app_state for backward compatibility (can be removed later)
                self.ws_client.send_message("app_state", {
                    "url": url,
                    "state": state
                })
                
                # Update database
                self.db[self.tasks_collection_name].update_one(
                    {"_id": self.task_id},
                    {"$set": {
                        "app_state": {
                            "url": url,
                            "state": state
                        },
                        "preview_status": preview_data
                    }}
                )
                

            except Exception as e: 
                print(f"Error in app_state_callback: {e}")
                import traceback
                traceback.print_exc()
        except Exception as e:
            print(f"Error in app state callback thread: {e}")
        finally:
            print(f"App state callback thread {threading.current_thread().name} completed")

    def app_state_callback(self, url, state=None, container=None):
        """Public method to trigger app state callback in background thread"""
        return self._app_state_callback_thread(url, state, container)
        
    @run_in_daemon_thread
    def _file_watch_callback_thread(self, type, file_path, content=None):
        """Thread-safe file watch callback"""
        try:
            print(f"Starting file watch callback in thread {threading.current_thread().name}")
            
            self.update_activity()

            if self.filewatch is None:
                self.filewatch = initialize_filewatch_dir()
                if self.filewatch is None:  # Still None after initialization
                    print("Warning: FileWatch could not be initialized")
                    return
                    
            if type == 'read_file':
                if not file_path:
                    print("Warning: Empty file path for read_file operation")
                    return
                self.filewatch.write_file(file_path)
                
            elif type == 'write_file':
                if not file_path:
                    print("Warning: Empty file path for write_file operation")
                    return
                self.ws_client.send_message("file_watch", self.filewatch.read_file_diff(file_path, content))
                
            elif type == 'edit_file':
                if not content:
                    print("Warning: Empty content for edit_file operation")
                    return
                self.ws_client.send_message("file_watch", self.filewatch.get_diff_content(content))
                
        except Exception as e:
            print(f"Error in file watch callback thread: {e}")
        finally:
            print(f"File watch callback thread {threading.current_thread().name} completed")

    def file_watch_callback(self, type, file_path, content=None):
        """Public method to trigger file watch callback in background thread"""  
        return self._file_watch_callback_thread(type, file_path, content)

    @run_in_daemon_thread
    def _function_call_callback_thread(self, call_description: FunctionCallDescription):
        """Thread-safe function call callback"""
        try:
            print(f"Starting function call callback in thread {threading.current_thread().name}")
            
            self.update_activity()
            # Run async code synchronously
            # org_data = Organization.get_sync(settings.KAVIA_ROOT_TENANT_ID)
            # organization = Organization(**org_data)
            # # print("Organization", organization.model_dump())
            call_dict = self._convert_to_serializable(call_description)
            call_dict['timestamp']=generate_timestamp() 
            # if organization.settings.showfunctioncalling:
            self.ws_client.send_message("function_call", {"call": call_dict})
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id},
                {"$push": {"function_calls": call_dict}}
            )               
            if call_dict['function_name']  == 'write_file':
                print("uploading to s3")
                upload_and_process(
                    identifier=self.task_id,
                    file_content=call_dict['arguments'].get('content', '').encode('utf-8'), 
                    file_name=call_dict['arguments'].get('file_path').split('/')[-1], 
                    content_type='text/markdown',
                    tenant_id=get_tenant_id(),
                    folder_name=self.docs_folder
                )
                self.file_watch_callback(call_dict['function_name'], call_dict['arguments'].get('file_path', ''), call_dict['arguments'].get('content', ''))
            elif call_dict['function_name'] == 'read_file':
                self.file_watch_callback(call_dict['function_name'], call_dict['arguments'].get('file_path', ''), call_dict['arguments'].get('content', ''))
            elif call_dict['function_name'] == 'edit_file':
                self.file_watch_callback('edit_file', '', call_dict['arguments'].get('changes', ''))
            
        except Exception as e:
            print(f"Error in function call callback thread: {e}")
        finally:
            print(f"Function call callback thread {threading.current_thread().name} completed")

    def function_call_callback(self, call_description: FunctionCallDescription):
        """Public method to trigger function call callback in background thread"""
        return self._function_call_callback_thread(call_description)

    @staticmethod
    def _convert_to_serializable(obj):
        if isinstance(obj, OrderedDict):
            return dict(obj)
        elif hasattr(obj, '__dict__'):
            return {k: TaskReporter._convert_to_serializable(v) 
                   for k, v in obj.__dict__.items() 
                   if not k.startswith('_')}
        return obj

    @run_in_daemon_thread
    def _task_start_callback_thread(self, task_description):
        """Thread-safe task start callback"""
        try:
            print(f"Starting task start callback in thread {threading.current_thread().name}")
            
            self.ws_client.send_message("task_start", {"description": task_description})
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id}, 
                {"$set": {"status": "RUNNING", "description": task_description}}
            )
        except Exception as e:
            print(f"Error in task start callback thread: {e}")
        finally:
            print(f"Task start callback thread {threading.current_thread().name} completed")

    def task_start_callback(self, task_description):
        """Public method to trigger task start callback in background thread"""
        return self._task_start_callback_thread(task_description)

    @run_in_daemon_thread
    def _user_query_callback_thread(self, questions):
        """Thread-safe user query callback"""
        try:
            print(f"Starting user query callback in thread {threading.current_thread().name}")
            
            self.ws_client.send_message("user_query", {"questions": questions})
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id}, 
                {"$set": {"questions": questions}}
            )
        except Exception as e:
            print(f"Error in user query callback thread: {e}")
        finally:
            print(f"User query callback thread {threading.current_thread().name} completed")

    def user_query_callback(self, questions):
        """Public method to trigger user query callback in background thread"""
        return self._user_query_callback_thread(questions)

    @run_in_daemon_thread
    def _send_agent_message_thread(self, message):
        """Thread-safe send agent message"""
        try:
            print(f"Starting send agent message in thread {threading.current_thread().name}")
            
            print("Sending agent message", message)
            message_obj = Message(content=message, sender="AI", timestamp=generate_timestamp())
            self.ws_client.send_message("agent_message", message_obj.to_dict())
                
            self.db[self.tasks_collection_name].update_one(
                {"_id": self.task_id},
                {"$push": {"messages": message_obj.to_dict()}}
            )
        except Exception as e:
            print(f"Error in send agent message thread: {e}")
        finally:
            print(f"Send agent message thread {threading.current_thread().name} completed")

    def send_agent_message(self, message):
        """Public method to trigger send agent message in background thread"""
        return self._send_agent_message_thread(message)

    def cleanup(self):
        """Cleanup websocket connection and wait for threads"""
        try:
            self.is_user_input_requested = False
            if self.ws_client:
                self.ws_client.stop_message_handler()
                self.ws_client.disconnect()
            
            # Wait for active threads to complete (with timeout)
            print(f"Waiting for {len(self._active_threads)} active threads to complete...")
            for thread in list(self._active_threads):
                if thread.is_alive():
                    thread.join(timeout=5.0)  # 5 second timeout per thread
                    
        except Exception as e:
            print(f"Error during reporter cleanup: {e}")