import asyncio
import os
import re
from typing import Optional
from llm_wrapper.core.llm_interface import LLMInterface
from functools import wraps
from concurrent.futures import ThreadPoolExecutor
import json

def sync_async(async_func):
    @wraps(async_func)
    def sync_wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(async_func(*args, **kwargs))
        finally:
            loop.close()
    return sync_wrapper

class GitLLM:
    def __init__(self, task_id, logs_path):

        path = os.path.join(logs_path)
        
        self.llm = LLMInterface(
            path,
            'GitAgent',
            project_id=123,
            user_id=123,
            agent_name="git_helper",
        )
        self.model = 'gpt-4o-mini'
        self._executor = ThreadPoolExecutor(max_workers=1)

    async def _async_sanitize_and_verify(self, content: str) -> tuple[str, Optional[str]]:
        """Async implementation of sanitize and verify"""
        prompt = f"""
        Please analyze this git command output and:
        1. Remove any sensitive information (auth tokens, full repository URLs, credentials)
        2. Check for errors or issues
        3. If there are errors, suggest how to resolve them
        4. Format the output in a clear, readable way

        Git Output:
        {content}

        Respond in this format:
        SANITIZED_OUTPUT: <the cleaned output>
        ERROR_DETECTED: <yes/no>
        RESOLUTION: <resolution steps if error detected, or 'None' if no error>
        """

        try:
            response = await self.llm.llm_interaction_wrapper(
                messages=[],
                user_prompt=prompt,
                system_prompt="You are helpful assistant.",
                response_format={'type': 'text'},
                model=self.model,
                stream=False,
            )
            
            # Parse LLM response
            sanitized_output = re.search(r'SANITIZED_OUTPUT:(.*?)(?=ERROR_DETECTED:|$)', response, re.DOTALL)
            error_detected = re.search(r'ERROR_DETECTED:(.*?)(?=RESOLUTION:|$)', response, re.DOTALL)
            resolution = re.search(r'RESOLUTION:(.*?)$', response, re.DOTALL)

            sanitized_content = sanitized_output.group(1).strip() if sanitized_output else content
            has_error = error_detected.group(1).strip().lower() == 'yes' if error_detected else False
            error_resolution = resolution.group(1).strip() if resolution and has_error else None

            # Replace any remaining sensitive patterns
            sanitized_content = self.remove_sensitive_patterns(sanitized_content)
            
            return sanitized_content, error_resolution

        except Exception as e:
            print(f"Error in LLM verification: {str(e)}")
            return self.remove_sensitive_patterns(content), None

    async def generate_commit_message(self, diff_output: str, context: dict = None) -> str:
        """
        Generate an intelligent commit message based on code changes and context.
        
        Args:
            diff_output: Git diff output showing code changes
            context: Optional dictionary containing additional context like:
                    - status: Current execution status
                    - latest_result: Latest operation result
                    - step_info: Information about the current step
                    - request_context: Any additional request context
                    
        Returns:
            str: Generated commit message or None if generation fails
        """
        try:
            # Build context section of prompt
            context_sections = []
            if context:
                for key, value in context.items():
                    if value:
                        if isinstance(value, (dict, list)):
                            value = json.dumps(value, indent=2)
                        context_sections.append(f"{key.replace('_', ' ').title()}: {value}")

            context_text = "\n".join(context_sections) if context_sections else "No additional context provided"
            prompt = f"""
            Please analyze the following code changes and context to generate a concise but descriptive commit message:

            Context Information:
            {context_text}
            
            Git Diff:
            {diff_output}

            Generate a commit message that:
            1. Summarizes the main changes
            2. Includes the context of why these changes were made
            3. Is clear and follows good commit message practices
            4. Starts with a category (feat/fix/refactor/etc) where appropriate
            5. Uses conventional commit format: <type>(<scope>): <description>
            6. Keeps the message under 72 characters
            7. Uses imperative mood ("add" not "added")

            Respond with just the commit message, no additional formatting.
            """

            response = await self.llm.llm_interaction_wrapper(
                messages=[],
                user_prompt=prompt,
                system_prompt="You are an expert at writing clear, concise, and meaningful git commit messages following best practices.",
                response_format={'type': 'text'},
                model=self.model,
                stream=False,
            )
            print(response)
            print(type(response))
            # Clean and validate the commit message
            commit_message = response.strip() if response else None
            if commit_message:
                # Remove any extra formatting or quotes
                commit_message = re.sub(r'^["\']|["\']$', '', commit_message)
                commit_message = commit_message.strip()
                
                # Validate conventional commit format
                if not re.match(r'^(feat|fix|docs|style|refactor|perf|test|chore|ci|build|revert)(\(.+\))?: .+', commit_message):
                    commit_message = f"chore: {commit_message}"

            return commit_message

        except Exception as e:
            print(f"Error generating commit message: {e}")
            return None
    
    def generate_commit_message_sync(self, diff_output: str, context: dict = None) -> str:
        """
        Synchronous wrapper for generate_commit_message
        """
        future = self._executor.submit(
            sync_async(self.generate_commit_message),
            diff_output,
            context
        )
        return future.result()
    
    def sanitize_and_verify_output(self, content: str) -> tuple[str, Optional[str]]:
        """Synchronous wrapper for the async sanitize and verify function"""
        future = self._executor.submit(
            sync_async(self._async_sanitize_and_verify),
            content
        )
        return future.result()

    def remove_sensitive_patterns(self, content: str) -> str:
        """Remove common sensitive patterns from content"""
        patterns = [
            (r'https?://[^/]*@[^\s]+', '[REPOSITORY_URL]'),  # Auth URLs
            (r'ghp_[a-zA-Z0-9]{36}', '[TOKEN]'),  # GitHub tokens
            (r'git@[^\s]+', '[GIT_URL]'),  # SSH URLs
        ]
        
        result = content
        for pattern, replacement in patterns:
            result = re.sub(pattern, replacement, result)
        return result