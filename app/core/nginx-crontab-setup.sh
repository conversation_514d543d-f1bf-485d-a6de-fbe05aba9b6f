#!/bin/bash
# Clean Ubuntu setup for Nginx cleanup automation
# Runs every 5 seconds with live log updates

set -e

echo "=== Ubuntu Nginx Cleanup Setup ==="

# Install required tools
echo "Installing required packages..."
apt-get update -q
apt-get install -y cron procps

# Start cron service
echo "Starting cron service..."
service cron start

# Simple verification - just check if cron started
echo "Cron service started"

# Create main nginx cleanup script
cat > /root/nginx.sh << 'EOF'
#!/bin/bash
set -e
NGINX_CONF_DIR="/etc/nginx/conf.d"
DRY_RUN=false
if [[ "$1" == "--dry-run" ]]; then
  DRY_RUN=true
fi

find_and_remove_problematic_files() {
  local temp_file=$(mktemp)
  nginx -s reload 2> "$temp_file" || true
  local files=($(grep -o '/etc/nginx/conf.d/[^ :]*' "$temp_file" | sort -u))
  
  if [[ ${#files[@]} -eq 0 ]]; then
    rm "$temp_file"
    return 1
  fi
  
  for file in "${files[@]}"; do
    if [[ "$DRY_RUN" == "false" ]]; then
      if [[ -f "$file" ]]; then
        rm "$file"
      fi
    fi
  done
  
  rm "$temp_file"
  return 0
}

MAX_ATTEMPTS=10
attempt=1
while [[ $attempt -le $MAX_ATTEMPTS ]]; do
  if find_and_remove_problematic_files; then
    if [[ "$DRY_RUN" == "false" ]]; then
      nginx -s reload 2>/dev/null || true
    fi
  else
    break
  fi
  
  if nginx -t &>/dev/null; then
    break
  fi
  
  attempt=$((attempt + 1))
done

if [[ "$DRY_RUN" == "false" ]]; then
  nginx -s reload 2>/dev/null || true
fi
EOF

# Create cron wrapper that logs every 5 seconds
cat > /root/nginx-cleanup-cron.sh << 'EOF'
#!/bin/bash
LOGFILE="/tmp/nginx-cleanup.log"

for i in {0..11}; do
    echo "$(date): Running cleanup cycle $((i+1))/12" >> "$LOGFILE"
    /root/nginx.sh >/dev/null 2>&1
    [ $i -lt 11 ] && sleep 5
done
echo "$(date): Completed full minute cycle" >> "$LOGFILE"
EOF

# Make scripts executable
chmod +x /root/nginx.sh
chmod +x /root/nginx-cleanup-cron.sh

# Create log file
touch /tmp/nginx-cleanup.log

# Add to crontab
crontab -l 2>/dev/null | grep -v "nginx-cleanup-cron.sh" | crontab - 2>/dev/null || true
(crontab -l 2>/dev/null; echo "* * * * * /root/nginx-cleanup-cron.sh") | crontab -

echo ""
echo "=== Setup Complete ==="
echo "✓ Cron installed and running"
echo "✓ Nginx cleanup script created: /root/nginx.sh"
echo "✓ Cron wrapper created: /root/nginx-cleanup-cron.sh"
echo "✓ Crontab configured (every minute = 12 runs every 5 seconds)"
echo "✓ Log file: /tmp/nginx-cleanup.log"
echo ""
echo "Verification:"
echo "  crontab -l"
echo "  tail -f /tmp/nginx-cleanup.log"
echo "  ps aux | grep cron"